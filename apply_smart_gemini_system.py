# -*- coding: utf-8 -*-
"""
تطبيق النظام الذكي لإدارة مفاتيح Gemini على الكود الحالي
يقوم بتحديث الملف الرئيسي تلقائياً
"""

import os
import re
import shutil
from datetime import datetime

def apply_smart_gemini_integration():
    """تطبيق النظام الذكي على الملف الرئيسي"""
    
    main_file = "mod_processor_broken_final.py"
    
    if not os.path.exists(main_file):
        print(f"❌ الملف الرئيسي غير موجود: {main_file}")
        return False
    
    print("🚀 بدء تطبيق النظام الذكي لإدارة مفاتيح Gemini...")
    
    try:
        # قراءة الملف الحالي
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود التحديث مسبقاً
        if "smart_gemini_key_manager" in content:
            print("⚠️ النظام الذكي مطبق مسبقاً")
            return True
        
        # إضافة الاستيرادات في بداية الملف
        import_section = '''
# استيراد النظام الذكي لإدارة مفاتيح Gemini
try:
    from smart_gemini_key_manager import (
        SmartGeminiKeyManager, 
        initialize_gemini_manager, 
        get_gemini_manager,
        smart_gemini_request as smart_request,
        get_gemini_stats,
        test_gemini_keys,
        reset_gemini_cooldowns,
        get_gemini_health_report
    )
    from enhanced_description_generator import (
        EnhancedDescriptionGenerator,
        generate_enhanced_descriptions
    )
    SMART_GEMINI_AVAILABLE = True
    print("✅ تم تحميل النظام الذكي لإدارة مفاتيح Gemini")
except ImportError as e:
    print(f"⚠️ النظام الذكي لـ Gemini غير متوفر: {e}")
    SMART_GEMINI_AVAILABLE = False

# متغير عام للمدير الذكي
smart_gemini_manager = None
'''
        
        # البحث عن مكان إدراج الاستيرادات (بعد الاستيرادات الموجودة)
        import_pattern = r'(import\s+.*?\n|from\s+.*?\n)+'
        import_match = re.search(import_pattern, content, re.MULTILINE)
        
        if import_match:
            insert_pos = import_match.end()
            content = content[:insert_pos] + import_section + content[insert_pos:]
        else:
            # إذا لم نجد استيرادات، أضف في البداية
            content = import_section + content
        
        # إضافة دالة التهيئة
        init_function = '''
def initialize_smart_gemini_system():
    """تهيئة النظام الذكي لإدارة مفاتيح Gemini"""
    global smart_gemini_manager
    
    if not SMART_GEMINI_AVAILABLE:
        print("❌ النظام الذكي غير متوفر")
        return False
    
    try:
        smart_gemini_manager = initialize_gemini_manager()
        if smart_gemini_manager and len(smart_gemini_manager.keys) > 0:
            print(f"🚀 تم تهيئة النظام الذكي مع {len(smart_gemini_manager.keys)} مفتاح")
            
            # طباعة إحصائيات أولية
            stats = smart_gemini_manager.get_stats_summary()
            print(f"📊 مفاتيح متاحة: {stats['available_keys']}")
            
            return True
        else:
            print("❌ لا توجد مفاتيح API متاحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تهيئة النظام الذكي: {e}")
        return False

def enhanced_smart_gemini_request(prompt: str, max_retries: int = 3) -> str:
    """طلب ذكي محسن باستخدام النظام الجديد"""
    global smart_gemini_manager
    
    if smart_gemini_manager and SMART_GEMINI_AVAILABLE:
        try:
            response = smart_gemini_manager.smart_request(prompt, max_retries)
            if response:
                return response
        except Exception as e:
            print(f"❌ خطأ في النظام الذكي: {e}")
    
    # العودة للنظام القديم
    return smart_gemini_request(prompt, max_retries)

def enhanced_generate_descriptions_wrapper(mod_data, extracted_content=""):
    """دالة مساعدة لإنشاء أوصاف محسنة"""
    global smart_gemini_manager
    
    if smart_gemini_manager and SMART_GEMINI_AVAILABLE:
        try:
            generator = EnhancedDescriptionGenerator()
            return generator.generate_descriptions(mod_data, extracted_content)
        except Exception as e:
            print(f"❌ خطأ في مولد الأوصاف المحسن: {e}")
    
    # العودة للنظام القديم
    return get_fallback_descriptions(mod_data)

'''
        
        # البحث عن مكان إدراج الدوال (قبل دالة main أو في نهاية الملف)
        main_pattern = r'def main\(\):'
        main_match = re.search(main_pattern, content)
        
        if main_match:
            insert_pos = main_match.start()
            content = content[:insert_pos] + init_function + "\n" + content[insert_pos:]
        else:
            # إذا لم نجد دالة main، أضف في النهاية
            content += "\n" + init_function
        
        # تحديث استدعاءات smart_gemini_request
        content = re.sub(
            r'smart_gemini_request\(',
            'enhanced_smart_gemini_request(',
            content
        )
        
        # إضافة استدعاء التهيئة في دالة main أو بداية التطبيق
        if 'def main():' in content:
            # البحث عن بداية دالة main وإضافة التهيئة
            main_start = content.find('def main():')
            if main_start != -1:
                # البحث عن نهاية السطر الأول في دالة main
                main_line_end = content.find('\n', main_start)
                if main_line_end != -1:
                    # إضافة استدعاء التهيئة
                    init_call = '\n    # تهيئة النظام الذكي لإدارة مفاتيح Gemini\n    initialize_smart_gemini_system()\n'
                    content = content[:main_line_end] + init_call + content[main_line_end:]
        
        # حفظ الملف المحدث
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تطبيق النظام الذكي بنجاح!")
        print("🔧 التحديثات المطبقة:")
        print("   • إضافة استيرادات النظام الذكي")
        print("   • إضافة دوال التهيئة والتحسين")
        print("   • تحديث استدعاءات smart_gemini_request")
        print("   • إضافة استدعاء التهيئة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق النظام الذكي: {e}")
        return False

def create_quick_start_guide():
    """إنشاء دليل البدء السريع"""
    
    guide_content = """# دليل البدء السريع للنظام الذكي لإدارة مفاتيح Gemini

## 🚀 تم تطبيق النظام بنجاح!

### ✅ ما تم تحديثه:
1. **إضافة النظام الذكي**: تم دمج مدير المفاتيح الذكي مع الكود الحالي
2. **تحسين الأوصاف**: الآن ستحصل على أوصاف أطول وأكثر تفصيلاً
3. **تبديل تلقائي**: عند فشل مفتاح، سيتم التبديل تلقائياً للمفتاح التالي
4. **مراقبة الأداء**: تتبع أداء كل مفتاح وإحصائيات مفصلة

### 🎯 الميزات الجديدة:

#### 🔑 إدارة ذكية للمفاتيح:
- **66 مفتاح API**: استخدام جميع مفاتيحك بذكاء
- **تبديل تلقائي**: عند وصول مفتاح للحد الأقصى
- **فترات تبريد**: منع استخدام المفاتيح المحظورة مؤقتاً
- **أولويات ذكية**: المفاتيح الأفضل تُستخدم أولاً

#### 📝 أوصاف محسنة:
- **أطول وأكثر تفصيلاً**: 300+ كلمة بدلاً من الأوصاف القصيرة
- **جودة أفضل**: تحليل محتوى المود لإنشاء أوصاف دقيقة
- **باللغتين**: عربي وإنجليزي عالي الجودة
- **إعادة محاولة ذكية**: إذا كان الوصف قصير، يعيد المحاولة

#### 📊 مراقبة وإحصائيات:
- **تتبع الأداء**: معدل نجاح كل مفتاح
- **إحصائيات مفصلة**: عدد الطلبات، الأخطاء، أوقات الاستجابة
- **تقارير صحة**: حالة كل مفتاح في الوقت الفعلي
- **تصدير البيانات**: حفظ الإحصائيات في ملفات JSON

### 🔧 كيفية الاستخدام:

#### 1. تشغيل الأداة عادياً:
```bash
python mod_processor_broken_final.py
```

#### 2. مراقبة الرسائل:
ستظهر رسائل مثل:
- `✅ تم تحميل النظام الذكي لإدارة مفاتيح Gemini`
- `🚀 تم تهيئة النظام الذكي مع 66 مفتاح`
- `📊 مفاتيح متاحة: 66`

#### 3. إنشاء الأوصاف:
- استخدم أزرار إنشاء الأوصاف كالمعتاد
- ستلاحظ أوصاف أطول وأكثر تفصيلاً
- التبديل التلقائي عند مشاكل المفاتيح

#### 4. مراقبة الأداء:
- راقب رسائل وحدة التحكم للتبديل بين المفاتيح
- تحقق من ملف `gemini_stats.json` للإحصائيات

### 🛠️ استكشاف الأخطاء:

#### إذا ظهرت رسالة "النظام الذكي غير متوفر":
1. تأكد من وجود الملفات:
   - `smart_gemini_key_manager.py`
   - `enhanced_description_generator.py`
2. تحقق من تثبيت المكتبات المطلوبة:
   ```bash
   pip install google-generativeai
   ```

#### إذا لم تتحسن الأوصاف:
1. تحقق من صحة مفاتيح API في `api_keys.json`
2. راقب رسائل وحدة التحكم للأخطاء
3. تأكد من وجود رصيد في مفاتيح Google AI

#### للحصول على إحصائيات مفصلة:
```python
# تشغيل اختبار النظام
python test_smart_gemini_system.py
```

### 📈 النتائج المتوقعة:

#### قبل النظام الذكي:
- أوصاف قصيرة (50-100 كلمة)
- فشل عند مشاكل المفتاح الأول
- لا توجد مراقبة للأداء

#### بعد النظام الذكي:
- أوصاف مفصلة (300+ كلمة)
- تبديل تلقائي بين 66 مفتاح
- مراقبة شاملة للأداء
- موثوقية أعلى بكثير

### 🎉 تهانينا!
النظام الذكي يعمل الآن وسيحسن بشكل كبير من جودة الأوصاف وموثوقية الأداة.

---
**ملاحظة**: إذا واجهت أي مشاكل، راجع ملف `INSTALLATION_GUIDE.md` للتعليمات المفصلة.
"""
    
    with open("QUICK_START_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 تم إنشاء دليل البدء السريع: QUICK_START_GUIDE.md")

def main():
    """الدالة الرئيسية"""
    print("🔧 تطبيق النظام الذكي لإدارة مفاتيح Gemini")
    print("="*60)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        "smart_gemini_key_manager.py",
        "enhanced_description_generator.py",
        "api_keys.json"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مطلوبة مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        print("\n💡 تأكد من تشغيل integrate_smart_gemini_system.py أولاً")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # تطبيق النظام الذكي
    if apply_smart_gemini_integration():
        print("\n🎉 تم تطبيق النظام الذكي بنجاح!")
        
        # إنشاء دليل البدء السريع
        create_quick_start_guide()
        
        print("\n📋 الخطوات التالية:")
        print("1. شغل الأداة عادياً: python mod_processor_broken_final.py")
        print("2. جرب إنشاء وصف لمود وستلاحظ التحسن")
        print("3. راقب رسائل وحدة التحكم للتبديل الذكي")
        print("4. اقرأ QUICK_START_GUIDE.md للتفاصيل")
        
        return True
    else:
        print("❌ فشل في تطبيق النظام الذكي")
        return False

if __name__ == "__main__":
    main()

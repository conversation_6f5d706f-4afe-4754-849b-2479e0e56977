# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام الذكي لإدارة مفاتيح Gemini API
يتضمن اختبارات للمدير، مولد الأوصاف، والتكامل
"""

import time
import json
from datetime import datetime
from smart_gemini_key_manager import SmartGeminiKeyManager, initialize_gemini_manager
from enhanced_description_generator import EnhancedDescriptionGenerator

def test_key_manager():
    """اختبار مدير المفاتيح الذكي"""
    print("\n" + "="*60)
    print("🧪 اختبار مدير المفاتيح الذكي")
    print("="*60)
    
    try:
        # إنشاء مدير المفاتيح
        manager = SmartGeminiKeyManager()
        
        # طباعة الإحصائيات الأولية
        print(f"🔑 تم تحميل {len(manager.keys)} مفتاح")
        
        stats = manager.get_stats_summary()
        print(f"📊 مفاتيح متاحة: {stats['available_keys']}")
        
        # اختبار الحصول على أفضل مفتاح
        best_key = manager.get_best_key()
        if best_key:
            key_index, key_info = best_key
            print(f"🎯 أفضل مفتاح: {key_index + 1} (أولوية: {key_info.priority})")
        
        # اختبار طلب بسيط
        print("\n🔍 اختبار طلب بسيط...")
        response = manager.smart_request("مرحبا، قل 'النظام يعمل بشكل صحيح'")
        
        if response:
            print(f"✅ استجابة الاختبار: {response[:100]}...")
            
            # طباعة الإحصائيات بعد الطلب
            updated_stats = manager.get_stats_summary()
            print(f"📈 معدل النجاح: {updated_stats['success_rate']}%")
            print(f"⏱️ متوسط وقت الاستجابة: {updated_stats['avg_response_time']}s")
        else:
            print("❌ فشل في الحصول على استجابة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المدير: {e}")
        return False

def test_description_generator():
    """اختبار مولد الأوصاف المحسن"""
    print("\n" + "="*60)
    print("🧪 اختبار مولد الأوصاف المحسن")
    print("="*60)
    
    try:
        # إنشاء مولد الأوصاف
        generator = EnhancedDescriptionGenerator()
        
        # بيانات تجريبية للمود
        test_mod_data = {
            'name': 'DecoDrop Addon',
            'category': 'Addons',
            'creator_name': 'TestCreator',
            'version': '1.0.0'
        }
        
        test_content = """
        This addon adds beautiful decorative blocks and items to Minecraft.
        Features include new furniture, decorations, and building materials.
        Perfect for builders who want to create more detailed and beautiful structures.
        """
        
        print("🚀 إنشاء أوصاف تجريبية...")
        
        # إنشاء الأوصاف
        english_desc, arabic_desc = generator.generate_descriptions(test_mod_data, test_content)
        
        # تحليل النتائج
        print(f"\n📝 نتائج الاختبار:")
        print(f"   • طول الوصف الإنجليزي: {len(english_desc)} حرف")
        print(f"   • طول الوصف العربي: {len(arabic_desc)} حرف")
        
        # التحقق من جودة الأوصاف
        min_length = 200
        english_quality = len(english_desc) >= min_length
        arabic_quality = len(arabic_desc) >= min_length
        
        print(f"   • جودة الوصف الإنجليزي: {'✅' if english_quality else '❌'}")
        print(f"   • جودة الوصف العربي: {'✅' if arabic_quality else '❌'}")
        
        # عرض عينات من الأوصاف
        print(f"\n📖 عينة من الوصف الإنجليزي:")
        print(f"   {english_desc[:150]}...")
        
        print(f"\n📖 عينة من الوصف العربي:")
        print(f"   {arabic_desc[:150]}...")
        
        return english_quality and arabic_quality
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مولد الأوصاف: {e}")
        return False

def test_key_rotation():
    """اختبار نظام تبديل المفاتيح"""
    print("\n" + "="*60)
    print("🧪 اختبار نظام تبديل المفاتيح")
    print("="*60)
    
    try:
        manager = SmartGeminiKeyManager()
        
        if len(manager.keys) < 2:
            print("⚠️ يحتاج الاختبار لمفتاحين على الأقل")
            return True
        
        print(f"🔄 اختبار التبديل بين {len(manager.keys)} مفتاح...")
        
        # محاكاة أخطاء لاختبار التبديل
        original_key_index = manager.current_key_index
        
        # تسجيل خطأ في المفتاح الحالي
        manager.mark_key_error(original_key_index, "اختبار خطأ", manager.keys[original_key_index].status.__class__.QUOTA_EXCEEDED)
        
        # محاولة الحصول على مفتاح جديد
        new_best_key = manager.get_best_key()
        
        if new_best_key:
            new_key_index, _ = new_best_key
            if new_key_index != original_key_index:
                print(f"✅ تم التبديل من المفتاح {original_key_index + 1} إلى {new_key_index + 1}")
                
                # اختبار طلب بالمفتاح الجديد
                if manager.configure_client(new_key_index):
                    response = manager.smart_request("اختبار المفتاح الجديد")
                    if response:
                        print("✅ المفتاح الجديد يعمل بشكل صحيح")
                        return True
                    else:
                        print("⚠️ المفتاح الجديد لا يستجيب")
                        return False
            else:
                print("⚠️ لم يتم التبديل للمفتاح (قد يكون المفتاح الوحيد المتاح)")
                return True
        else:
            print("❌ لا توجد مفاتيح أخرى متاحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التبديل: {e}")
        return False

def test_stats_and_monitoring():
    """اختبار نظام الإحصائيات والمراقبة"""
    print("\n" + "="*60)
    print("🧪 اختبار نظام الإحصائيات والمراقبة")
    print("="*60)
    
    try:
        manager = SmartGeminiKeyManager()
        
        # اختبار الإحصائيات الأساسية
        stats = manager.get_stats_summary()
        print("📊 الإحصائيات الأساسية:")
        for key, value in stats.items():
            print(f"   • {key}: {value}")
        
        # اختبار تقرير الصحة
        print("\n🏥 اختبار تقرير الصحة...")
        health_report = manager.get_key_health_report()
        print("✅ تم إنشاء تقرير الصحة بنجاح")
        
        # اختبار تصدير الإحصائيات
        print("\n📁 اختبار تصدير الإحصائيات...")
        export_file = manager.export_stats_to_json("test_stats_export.json")
        
        if export_file:
            print(f"✅ تم تصدير الإحصائيات إلى: {export_file}")
            
            # التحقق من محتوى الملف
            try:
                with open(export_file, 'r', encoding='utf-8') as f:
                    exported_data = json.load(f)
                
                if 'summary' in exported_data and 'keys' in exported_data:
                    print("✅ بنية الملف المُصدر صحيحة")
                    return True
                else:
                    print("❌ بنية الملف المُصدر غير صحيحة")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في قراءة الملف المُصدر: {e}")
                return False
        else:
            print("❌ فشل في تصدير الإحصائيات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإحصائيات: {e}")
        return False

def test_performance():
    """اختبار الأداء والسرعة"""
    print("\n" + "="*60)
    print("🧪 اختبار الأداء والسرعة")
    print("="*60)
    
    try:
        manager = SmartGeminiKeyManager()
        
        # اختبار سرعة الطلبات
        test_prompts = [
            "مرحبا",
            "ما هو اليوم؟",
            "اكتب جملة قصيرة",
            "قل شكراً",
            "اختبار سريع"
        ]
        
        print(f"⏱️ اختبار سرعة {len(test_prompts)} طلبات...")
        
        start_time = time.time()
        successful_requests = 0
        
        for i, prompt in enumerate(test_prompts):
            print(f"   📤 طلب {i+1}: {prompt}")
            
            request_start = time.time()
            response = manager.smart_request(prompt)
            request_time = time.time() - request_start
            
            if response:
                successful_requests += 1
                print(f"   ✅ نجح في {request_time:.2f}s")
            else:
                print(f"   ❌ فشل بعد {request_time:.2f}s")
            
            # انتظار قصير بين الطلبات
            time.sleep(0.5)
        
        total_time = time.time() - start_time
        success_rate = (successful_requests / len(test_prompts)) * 100
        
        print(f"\n📊 نتائج اختبار الأداء:")
        print(f"   • إجمالي الوقت: {total_time:.2f}s")
        print(f"   • متوسط الوقت لكل طلب: {total_time/len(test_prompts):.2f}s")
        print(f"   • معدل النجاح: {success_rate:.1f}%")
        print(f"   • طلبات ناجحة: {successful_requests}/{len(test_prompts)}")
        
        # تحديث الإحصائيات النهائية
        final_stats = manager.get_stats_summary()
        print(f"   • متوسط وقت الاستجابة العام: {final_stats['avg_response_time']:.2f}s")
        
        return success_rate >= 80  # نجاح إذا كان معدل النجاح 80% أو أكثر
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")
        return False

def run_comprehensive_test():
    """تشغيل اختبار شامل للنظام"""
    print("🚀 بدء الاختبار الشامل للنظام الذكي لإدارة مفاتيح Gemini")
    print("="*80)
    
    tests = [
        ("مدير المفاتيح الذكي", test_key_manager),
        ("مولد الأوصاف المحسن", test_description_generator),
        ("نظام تبديل المفاتيح", test_key_rotation),
        ("الإحصائيات والمراقبة", test_stats_and_monitoring),
        ("الأداء والسرعة", test_performance)
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n🔍 تشغيل اختبار: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ نجح" if result else "❌ فشل"
            print(f"📊 نتيجة اختبار {test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    total_time = time.time() - start_time
    
    # تقرير النتائج النهائي
    print("\n" + "="*80)
    print("📋 تقرير النتائج النهائي")
    print("="*80)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
    
    print(f"\n📊 الإحصائيات النهائية:")
    print(f"   • اختبارات ناجحة: {passed_tests}/{total_tests}")
    print(f"   • معدل النجاح: {success_rate:.1f}%")
    print(f"   • إجمالي الوقت: {total_time:.2f} ثانية")
    
    if success_rate >= 80:
        print("\n🎉 النظام يعمل بشكل ممتاز!")
        print("✅ جاهز للاستخدام في الإنتاج")
    elif success_rate >= 60:
        print("\n⚠️ النظام يعمل بشكل جيد مع بعض المشاكل")
        print("🔧 قد تحتاج لمراجعة الاختبارات الفاشلة")
    else:
        print("\n❌ النظام يحتاج لمراجعة وإصلاح")
        print("🛠️ يرجى مراجعة الأخطاء وإعادة الاختبار")
    
    return results

if __name__ == "__main__":
    # تشغيل الاختبار الشامل
    results = run_comprehensive_test()
    
    # حفظ النتائج في ملف
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"test_results_{timestamp}.json"
    
    try:
        test_report = {
            "timestamp": datetime.now().isoformat(),
            "results": results,
            "summary": {
                "total_tests": len(results),
                "passed_tests": sum(results.values()),
                "success_rate": (sum(results.values()) / len(results)) * 100
            }
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(test_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 تم حفظ تقرير الاختبار في: {results_file}")
        
    except Exception as e:
        print(f"⚠️ خطأ في حفظ تقرير الاختبار: {e}")
    
    print("\n🏁 انتهى الاختبار الشامل")

# -*- coding: utf-8 -*-
"""
اختبار فصل المفاتيح للاستخراج والأوصاف
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_key_separation():
    """اختبار فصل المفاتيح"""
    print("🧪 اختبار فصل مفاتيح الاستخراج والأوصاف")
    print("="*60)
    
    try:
        # استيراد النظام
        from simple_key_rotation import SimpleKeyRotator
        
        # إنشاء مدير المفاتيح
        rotator = SimpleKeyRotator()
        
        print(f"🔑 تم تحميل {len(rotator.keys)} مفتاح")
        
        # اختبار الحصول على مفاتيح مختلفة
        extraction_key = rotator.get_extraction_key()
        description_key = rotator.get_description_key()
        
        print(f"\n🔍 مفتاح الاستخراج: {extraction_key[:12] if extraction_key else 'None'}...")
        print(f"📝 مفتاح الأوصاف: {description_key[:12] if description_key else 'None'}...")
        
        # التحقق من أن المفاتيح مختلفة
        if extraction_key and description_key and extraction_key != description_key:
            print("✅ المفاتيح مختلفة - ممتاز!")
            return True
        elif len(rotator.keys) < 2:
            print("⚠️ عدد المفاتيح قليل، لكن النظام يعمل")
            return True
        else:
            print("❌ المفاتيح متشابهة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_integration():
    """اختبار التكامل مع الكود الرئيسي"""
    print("\n🔗 اختبار التكامل مع الكود الرئيسي")
    print("="*60)
    
    try:
        # محاولة استيراد الدوال من الكود الرئيسي
        from mod_processor_broken_final import (
            configure_gemini_for_extraction,
            configure_gemini_for_descriptions,
            smart_gemini_request_with_key_type,
            initialize_smart_gemini_system
        )
        
        print("✅ تم استيراد الدوال بنجاح")
        
        # اختبار التهيئة
        result = initialize_smart_gemini_system()
        if result:
            print("✅ تم تهيئة النظام بنجاح")
        else:
            print("⚠️ تحذير في التهيئة، لكن النظام قد يعمل")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لفصل مفاتيح الاستخراج والأوصاف")
    print("="*80)
    
    # اختبار النظام الأساسي
    basic_test = test_key_separation()
    
    # اختبار التكامل
    integration_test = test_integration()
    
    print("\n" + "="*80)
    print("📋 نتائج الاختبار:")
    print(f"   • اختبار النظام الأساسي: {'✅ نجح' if basic_test else '❌ فشل'}")
    print(f"   • اختبار التكامل: {'✅ نجح' if integration_test else '❌ فشل'}")
    
    if basic_test and integration_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز لفصل مفاتيح الاستخراج والأوصاف")
        print("\n📋 ما تم تحقيقه:")
        print("   • مفاتيح مختلفة للاستخراج والأوصاف")
        print("   • تبديل تلقائي عند الأخطاء")
        print("   • تكامل مع الكود الحالي")
        print("\n🚀 الخطوات التالية:")
        print("   1. شغل الأداة: python mod_processor_broken_final.py")
        print("   2. جرب استخراج مود من MCPEDL")
        print("   3. جرب إنشاء أوصاف")
        print("   4. راقب الرسائل للتأكد من استخدام مفاتيح مختلفة")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("🔧 راجع الأخطاء أعلاه وأعد المحاولة")
    
    print("="*80)

if __name__ == "__main__":
    main()

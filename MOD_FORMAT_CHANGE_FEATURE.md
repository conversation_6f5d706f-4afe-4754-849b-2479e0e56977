# ميزة تغيير صيغة المودات الكاملة 🔄

## نظرة عامة

تم إضافة ميزة شاملة لتغيير صيغة المودات مع تحميل الملف الأصلي، تعديل صيغته، رفع الملف المحدث، وتحديث رابط التحميل في قاعدة البيانات.

## الميزات المضافة

### 1. 🔄 تغيير صيغة مود واحد
- **الموقع**: زر "🔄 تغيير صيغة مود" في أداة الإصلاح
- **الوظيفة**: تغيير صيغة مود محدد مع معالجة كاملة للملف

### 2. 📥 تحميل الملف الأصلي
- تحميل الملف من الرابط الحالي
- دعم جميع أنواع الملفات (.mcpack, .mcaddon, .zip)
- معالجة أخطاء التحميل

### 3. 🔧 تعديل صيغة الملف
- تحويل بين الصيغ المختلفة
- الحفاظ على محتوى الملف الأصلي
- تغيير الامتداد حسب الصيغة المطلوبة

### 4. 📤 رفع الملف المحدث
- رفع الملف إلى Supabase Storage
- إنشاء اسم ملف فريد مع timestamp
- إنشاء رابط عام جديد

### 5. 🗑️ حذف الملف القديم
- حذف الملف القديم من Supabase
- تنظيف التخزين من الملفات غير المستخدمة
- معالجة أخطاء الحذف

### 6. 🔄 تحديث قاعدة البيانات
- تحديث رابط التحميل في قاعدة البيانات
- تحديث البيانات المحلية
- تأكيد نجاح العملية

## كيفية الاستخدام

### الطريقة الكاملة (مع تحميل وتعديل الملف):

1. **فتح أداة الإصلاح**
   ```bash
   python mod_repair_tool.py
   ```

2. **تحميل قائمة المودات**
   - اضغط "تحميل قائمة المودات"

3. **تحديد المود**
   - حدد مود واحد من القائمة

4. **بدء تغيير الصيغة**
   - اضغط "🔄 تغيير صيغة مود"

5. **اختيار الصيغة الجديدة**
   - `.mcpack` - حزمة مود واحدة
   - `.mcaddon` - حزمة مودات متعددة
   - `.zip` - ملف مضغوط عادي

6. **تفعيل التحميل والرفع**
   - تأكد من تفعيل "تحميل الملف الأصلي، تعديل الصيغة، ورفع الملف المحدث"

7. **تطبيق التغيير**
   - اضغط "تطبيق التغيير"
   - انتظر انتهاء العملية

### الطريقة السريعة (تغيير الرابط فقط):

1. **إلغاء تفعيل التحميل**
   - ألغ تفعيل "تحميل الملف الأصلي..."

2. **تطبيق التغيير**
   - سيتم تغيير الرابط فقط بدون تحميل الملف

## العملية التفصيلية

### مراحل العملية:
```
[10%] جاري تحميل الملف الأصلي...
[30%] جاري تعديل صيغة الملف...
[50%] جاري رفع الملف المحدث...
[70%] جاري حذف الملف القديم...
[90%] جاري تحديث قاعدة البيانات...
[100%] ✅ تم تغيير صيغة المود بنجاح!
```

### الدوال المضافة:

#### 1. `download_mod_file(url)`
- تحميل الملف من الرابط
- معالجة أخطاء الشبكة
- إرجاع بيانات الملف

#### 2. `convert_file_format(file_data, current_ext, new_format)`
- تحويل صيغة الملف
- الحفاظ على المحتوى الأصلي
- إرجاع البيانات المحولة

#### 3. `upload_modified_mod_file(mod, file_data, new_format)`
- رفع الملف إلى Supabase
- إنشاء اسم ملف فريد
- إرجاع الرابط الجديد

#### 4. `delete_old_mod_file(old_url)`
- حذف الملف القديم
- تنظيف التخزين
- معالجة أخطاء الحذف

#### 5. `simple_url_change(mod, new_format, current_ext, dialog)`
- تغيير الرابط فقط
- بدون تحميل الملف
- تحديث سريع

#### 6. `sanitize_filename(filename)`
- تنظيف اسم الملف
- إزالة الأحرف غير المسموحة
- تحديد الطول الأقصى

## الواجهة المحسنة

### العناصر الجديدة:
- **معلومات المود**: عرض تفاصيل المود المحدد
- **اختيار الصيغة**: خيارات واضحة للصيغ المختلفة
- **خيارات التحميل**: تحكم في نوع العملية
- **شريط التقدم**: متابعة حالة العملية
- **رسائل الحالة**: تحديثات مباشرة للمستخدم

### التحذيرات:
- ⚠️ سيتم حذف الملف القديم واستبداله بالملف الجديد
- ⏳ هذه العملية قد تستغرق بضع دقائق

## معالجة الأخطاء

### الأخطاء المحتملة:
1. **فشل التحميل**: مشاكل الشبكة أو رابط غير صالح
2. **فشل التحويل**: مشاكل في معالجة الملف
3. **فشل الرفع**: مشاكل في Supabase Storage
4. **فشل الحذف**: مشاكل في حذف الملف القديم
5. **فشل التحديث**: مشاكل في قاعدة البيانات

### الإجراءات:
- عرض رسائل خطأ واضحة
- إيقاف العملية عند الفشل
- الاحتفاظ بالملف الأصلي عند الفشل
- تسجيل الأخطاء في سجل الحالة

## الفوائد

### 1. **مرونة كاملة**
- تغيير أي صيغة إلى أي صيغة أخرى
- خيار التحميل والرفع أو التغيير السريع

### 2. **أمان البيانات**
- تأكيدات قبل التغيير
- الاحتفاظ بالملف الأصلي حتى نجاح العملية
- معالجة شاملة للأخطاء

### 3. **سهولة الاستخدام**
- واجهة بديهية وواضحة
- شريط تقدم ورسائل حالة
- خيارات متعددة للمستخدم

### 4. **كفاءة التخزين**
- حذف الملفات القديمة تلقائياً
- أسماء ملفات فريدة ومنظمة
- تحديث روابط قاعدة البيانات

## الاختبارات

تم إنشاء ملف اختبار شامل: `test_mod_format_change.py`

### الاختبارات المشمولة:
- ✅ تحميل الملفات
- ✅ تحويل الصيغ
- ✅ تنظيف أسماء الملفات
- ✅ تعديل الروابط
- ✅ بنية بيانات المود
- ✅ محاكاة شريط التقدم
- ✅ معالجة الأخطاء

## الخلاصة

تم إضافة ميزة شاملة ومتقدمة لتغيير صيغة المودات مع:
- 🔄 تحميل وتعديل ورفع الملفات
- 🗑️ حذف الملفات القديمة
- 🔗 تحديث روابط قاعدة البيانات
- 🛡️ معالجة شاملة للأخطاء
- 🎨 واجهة مستخدم محسنة

**الميزة جاهزة للاستخدام الكامل!** 🎉

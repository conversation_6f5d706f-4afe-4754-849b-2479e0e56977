# -*- coding: utf-8 -*-
"""
ملف تكامل النظام الذكي لإدارة مفاتيح Gemini مع الكود الحالي
يقوم بتحديث الدوال الموجودة لاستخدام النظام الجديد
"""

import os
import shutil
from datetime import datetime

def backup_original_file():
    """إنشاء نسخة احتياطية من الملف الأصلي"""
    original_file = "mod_processor_broken_final.py"
    backup_file = f"mod_processor_broken_final_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
    
    try:
        shutil.copy2(original_file, backup_file)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_file}")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False

def create_integration_patch():
    """إنشاء ملف التحديث للدمج مع الكود الحالي"""
    
    patch_content = '''# -*- coding: utf-8 -*-
"""
تحديثات لدمج النظام الذكي لإدارة مفاتيح Gemini
يجب إضافة هذا الكود في بداية ملف mod_processor_broken_final.py
"""

# استيراد النظام الجديد
try:
    from smart_gemini_key_manager import (
        SmartGeminiKeyManager, 
        initialize_gemini_manager, 
        get_gemini_manager,
        smart_gemini_request as smart_request,
        get_gemini_stats,
        test_gemini_keys,
        reset_gemini_cooldowns,
        get_gemini_health_report
    )
    from enhanced_description_generator import (
        EnhancedDescriptionGenerator,
        generate_enhanced_descriptions
    )
    SMART_GEMINI_AVAILABLE = True
    print("✅ تم تحميل النظام الذكي لإدارة مفاتيح Gemini")
except ImportError as e:
    print(f"⚠️ النظام الذكي لـ Gemini غير متوفر: {e}")
    SMART_GEMINI_AVAILABLE = False

# متغير عام للمدير الذكي
smart_gemini_manager = None

def initialize_smart_gemini_system():
    """تهيئة النظام الذكي لإدارة مفاتيح Gemini"""
    global smart_gemini_manager
    
    if not SMART_GEMINI_AVAILABLE:
        print("❌ النظام الذكي غير متوفر")
        return False
    
    try:
        smart_gemini_manager = initialize_gemini_manager()
        if smart_gemini_manager and len(smart_gemini_manager.keys) > 0:
            print(f"🚀 تم تهيئة النظام الذكي مع {len(smart_gemini_manager.keys)} مفتاح")
            
            # طباعة إحصائيات أولية
            stats = smart_gemini_manager.get_stats_summary()
            print(f"📊 مفاتيح متاحة: {stats['available_keys']}")
            
            return True
        else:
            print("❌ لا توجد مفاتيح API متاحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تهيئة النظام الذكي: {e}")
        return False

def enhanced_generate_description_task(mod_name, mod_category, scraped_text, manual_features):
    """دالة محسنة لإنشاء الأوصاف باستخدام النظام الجديد"""
    global smart_gemini_manager
    
    try:
        update_status("🚀 بدء إنشاء الوصف باستخدام النظام الذكي...")
        
        if not smart_gemini_manager or not SMART_GEMINI_AVAILABLE:
            update_status("⚠️ النظام الذكي غير متوفر، استخدام النظام القديم...")
            return generate_description_task(mod_name, mod_category, scraped_text, manual_features)
        
        # تحضير بيانات المود
        mod_data = {
            'name': mod_name or 'Unknown Mod',
            'category': mod_category or 'Addon',
            'creator_name': 'Unknown Creator'
        }
        
        # دمج النصوص المستخرجة
        combined_content = ""
        if scraped_text:
            combined_content += scraped_text
        if manual_features:
            combined_content += f"\\n\\nميزات إضافية: {manual_features}"
        
        # استخدام المولد المحسن
        generator = EnhancedDescriptionGenerator()
        english_desc, arabic_desc = generator.generate_descriptions(mod_data, combined_content)
        
        # تحديث واجهة المستخدم
        if 'window' in globals() and window.winfo_exists():
            def update_gui():
                if 'description_text' in globals() and description_text.winfo_exists():
                    description_text.delete("1.0", tk.END)
                    description_text.insert("1.0", english_desc)
                
                update_status(f"✅ تم إنشاء وصف محسن ({len(english_desc)} حرف)")
                
                # إعادة تفعيل الزر
                if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists():
                    generate_desc_button.config(state=tk.NORMAL)
            
            window.after(0, update_gui)
        
        return english_desc
        
    except Exception as e:
        update_status(f"❌ خطأ في النظام المحسن: {e}")
        update_status("🔄 التبديل للنظام القديم...")
        
        # العودة للنظام القديم في حالة الخطأ
        if 'window' in globals() and window.winfo_exists():
            window.after(0, lambda: generate_desc_button.config(state=tk.NORMAL) if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists() else None)
        
        return generate_description_task(mod_name, mod_category, scraped_text, manual_features)

def enhanced_generate_arabic_description_task(mod_name, mod_category, scraped_text, manual_features):
    """دالة محسنة لإنشاء الأوصاف العربية باستخدام النظام الجديد"""
    global smart_gemini_manager
    
    try:
        update_status("🚀 بدء إنشاء الوصف العربي باستخدام النظام الذكي...")
        
        if not smart_gemini_manager or not SMART_GEMINI_AVAILABLE:
            update_status("⚠️ النظام الذكي غير متوفر، استخدام النظام القديم...")
            return generate_arabic_description_task(mod_name, mod_category, scraped_text, manual_features)
        
        # تحضير بيانات المود
        mod_data = {
            'name': mod_name or 'مود غير معروف',
            'category': mod_category or 'إضافة',
            'creator_name': 'مطور غير معروف'
        }
        
        # دمج النصوص المستخرجة
        combined_content = ""
        if scraped_text:
            combined_content += scraped_text
        if manual_features:
            combined_content += f"\\n\\nميزات إضافية: {manual_features}"
        
        # استخدام المولد المحسن
        generator = EnhancedDescriptionGenerator()
        english_desc, arabic_desc = generator.generate_descriptions(mod_data, combined_content)
        
        # تحديث واجهة المستخدم
        if 'window' in globals() and window.winfo_exists():
            def update_gui():
                if 'arabic_description_text' in globals() and arabic_description_text.winfo_exists():
                    arabic_description_text.delete("1.0", tk.END)
                    arabic_description_text.insert("1.0", arabic_desc)
                
                update_status(f"✅ تم إنشاء وصف عربي محسن ({len(arabic_desc)} حرف)")
                
                # إعادة تفعيل الزر
                if 'generate_arabic_desc_button' in globals() and generate_arabic_desc_button.winfo_exists():
                    generate_arabic_desc_button.config(state=tk.NORMAL)
            
            window.after(0, update_gui)
        
        return arabic_desc
        
    except Exception as e:
        update_status(f"❌ خطأ في النظام المحسن: {e}")
        update_status("🔄 التبديل للنظام القديم...")
        
        # العودة للنظام القديم في حالة الخطأ
        if 'window' in globals() and window.winfo_exists():
            window.after(0, lambda: generate_arabic_desc_button.config(state=tk.NORMAL) if 'generate_arabic_desc_button' in globals() and generate_arabic_desc_button.winfo_exists() else None)
        
        return generate_arabic_description_task(mod_name, mod_category, scraped_text, manual_features)

def enhanced_smart_gemini_request(prompt: str, max_retries: int = 3) -> str:
    """طلب ذكي محسن باستخدام النظام الجديد"""
    global smart_gemini_manager
    
    if smart_gemini_manager and SMART_GEMINI_AVAILABLE:
        try:
            response = smart_gemini_manager.smart_request(prompt, max_retries)
            if response:
                return response
        except Exception as e:
            print(f"❌ خطأ في النظام الذكي: {e}")
    
    # العودة للنظام القديم
    return smart_gemini_request(prompt, max_retries)

def show_smart_gemini_stats():
    """عرض إحصائيات النظام الذكي"""
    if not smart_gemini_manager or not SMART_GEMINI_AVAILABLE:
        messagebox.showinfo("معلومات", "النظام الذكي لإدارة مفاتيح Gemini غير متوفر")
        return
    
    try:
        # إنشاء نافذة الإحصائيات
        stats_window = tk.Toplevel()
        stats_window.title("إحصائيات النظام الذكي لمفاتيح Gemini")
        stats_window.geometry("800x600")
        stats_window.resizable(True, True)
        
        # إطار التمرير
        main_frame = ttk.Frame(stats_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # منطقة النص مع شريط التمرير
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        stats_text = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=stats_text.yview)
        stats_text.configure(yscrollcommand=scrollbar.set)
        
        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        def refresh_stats():
            stats_text.delete("1.0", tk.END)
            
            # الإحصائيات العامة
            stats = smart_gemini_manager.get_stats_summary()
            stats_text.insert(tk.END, "📊 إحصائيات عامة:\\n")
            stats_text.insert(tk.END, "=" * 50 + "\\n")
            stats_text.insert(tk.END, f"🔑 إجمالي المفاتيح: {stats['total_keys']}\\n")
            stats_text.insert(tk.END, f"✅ مفاتيح نشطة: {stats['active_keys']}\\n")
            stats_text.insert(tk.END, f"🟢 مفاتيح متاحة: {stats['available_keys']}\\n")
            stats_text.insert(tk.END, f"🕒 مفاتيح في تبريد: {stats['cooldown_keys']}\\n")
            stats_text.insert(tk.END, f"❌ مفاتيح غير صالحة: {stats['invalid_keys']}\\n")
            stats_text.insert(tk.END, f"📈 معدل النجاح: {stats['success_rate']}%\\n")
            stats_text.insert(tk.END, f"⏱️ متوسط وقت الاستجابة: {stats['avg_response_time']}s\\n")
            stats_text.insert(tk.END, f"📊 إجمالي الطلبات: {stats['total_requests']}\\n\\n")
            
            # تقرير الصحة المفصل
            health_report = smart_gemini_manager.get_key_health_report()
            stats_text.insert(tk.END, health_report)
        
        def test_all_keys():
            stats_text.insert(tk.END, "\\n🧪 بدء اختبار جميع المفاتيح...\\n")
            stats_text.update()
            
            results = smart_gemini_manager.test_all_keys()
            
            stats_text.insert(tk.END, f"\\n📊 نتائج الاختبار:\\n")
            stats_text.insert(tk.END, f"✅ مفاتيح تعمل: {results['working_keys']}\\n")
            stats_text.insert(tk.END, f"❌ مفاتيح فاشلة: {results['failed_keys']}\\n")
            
            refresh_stats()
        
        def reset_cooldowns():
            smart_gemini_manager.reset_key_cooldowns()
            stats_text.insert(tk.END, "\\n🔄 تم إعادة تعيين جميع فترات التبريد\\n")
            refresh_stats()
        
        def export_stats():
            filename = smart_gemini_manager.export_stats_to_json()
            if filename:
                stats_text.insert(tk.END, f"\\n📁 تم تصدير الإحصائيات إلى: {filename}\\n")
        
        # الأزرار
        ttk.Button(buttons_frame, text="تحديث الإحصائيات", command=refresh_stats).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="اختبار جميع المفاتيح", command=test_all_keys).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إعادة تعيين فترات التبريد", command=reset_cooldowns).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تصدير الإحصائيات", command=export_stats).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إغلاق", command=stats_window.destroy).pack(side=tk.RIGHT)
        
        # تحديث أولي
        refresh_stats()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في عرض الإحصائيات: {e}")

# دالة لإضافة زر الإحصائيات للواجهة الرئيسية
def add_smart_gemini_stats_button():
    """إضافة زر إحصائيات النظام الذكي للواجهة"""
    if 'gemini_management_frame' in globals():
        stats_button = ttk.Button(
            gemini_management_frame, 
            text="📊 إحصائيات النظام الذكي", 
            command=show_smart_gemini_stats
        )
        stats_button.pack(side=tk.LEFT, padx=5)

print("✅ تم تحميل ملف التكامل للنظام الذكي")
'''
    
    with open("smart_gemini_integration.py", 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print("✅ تم إنشاء ملف التكامل: smart_gemini_integration.py")

def create_installation_guide():
    """إنشاء دليل التثبيت"""
    
    guide_content = """# دليل تثبيت النظام الذكي لإدارة مفاتيح Gemini

## 📋 الخطوات المطلوبة:

### 1. نسخ الملفات الجديدة
تأكد من وجود الملفات التالية في نفس مجلد الأداة:
- `smart_gemini_key_manager.py`
- `enhanced_description_generator.py`
- `smart_gemini_integration.py`

### 2. تحديث الملف الرئيسي
أضف السطر التالي في بداية ملف `mod_processor_broken_final.py` (بعد الاستيرادات):

```python
# استيراد النظام الذكي لإدارة مفاتيح Gemini
exec(open('smart_gemini_integration.py', encoding='utf-8').read())
```

### 3. تحديث دوال إنشاء الأوصاف
استبدل الدوال التالية في `mod_processor_broken_final.py`:

#### استبدال `generate_description_task`:
```python
# استبدل السطر:
# run_in_thread(generate_description_task, mod_name, mod_category, scraped_context, manual_features_text)
# بـ:
run_in_thread(enhanced_generate_description_task, mod_name, mod_category, scraped_context, manual_features_text)
```

#### استبدال `generate_arabic_description_task`:
```python
# استبدل السطر:
# run_in_thread(generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)
# بـ:
run_in_thread(enhanced_generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)
```

### 4. تهيئة النظام
أضف السطر التالي في دالة `main()` أو في بداية التطبيق:

```python
# تهيئة النظام الذكي
initialize_smart_gemini_system()
```

### 5. إضافة زر الإحصائيات (اختياري)
أضف السطر التالي في مكان مناسب لإضافة زر الإحصائيات:

```python
add_smart_gemini_stats_button()
```

## 🎯 الميزات الجديدة:

### ✅ إدارة ذكية للمفاتيح:
- تبديل تلقائي عند فشل المفتاح
- تتبع حالة كل مفتاح
- فترات تبريد ذكية
- توزيع الحمل بين المفاتيح

### ✅ أوصاف محسنة:
- أوصاف أطول وأكثر تفصيلاً
- جودة أفضل باللغتين
- إعادة المحاولة الذكية
- تحليل محتوى المود

### ✅ مراقبة وإحصائيات:
- تتبع أداء كل مفتاح
- إحصائيات مفصلة
- تقارير صحة المفاتيح
- تصدير البيانات

## 🔧 استكشاف الأخطاء:

### إذا لم يعمل النظام:
1. تأكد من وجود جميع الملفات
2. تحقق من صحة مفاتيح API في `api_keys.json`
3. راجع رسائل الخطأ في وحدة التحكم
4. استخدم زر "اختبار جميع المفاتيح" للتشخيص

### للعودة للنظام القديم:
1. احذف أو علق على سطر استيراد `smart_gemini_integration.py`
2. أعد الدوال الأصلية
3. أعد تشغيل الأداة

## 📞 الدعم:
إذا واجهت أي مشاكل، تحقق من:
- ملف `gemini_stats.json` للإحصائيات
- رسائل وحدة التحكم للأخطاء
- حالة مفاتيح API في لوحة Google AI Studio
"""
    
    with open("INSTALLATION_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ تم إنشاء دليل التثبيت: INSTALLATION_GUIDE.md")

if __name__ == "__main__":
    print("🚀 بدء عملية التكامل للنظام الذكي...")
    
    # إنشاء نسخة احتياطية
    if backup_original_file():
        print("✅ تم إنشاء النسخة الاحتياطية")
    
    # إنشاء ملف التكامل
    create_integration_patch()
    
    # إنشاء دليل التثبيت
    create_installation_guide()
    
    print("\n" + "="*60)
    print("🎉 تم إنشاء جميع ملفات التكامل بنجاح!")
    print("="*60)
    print("📁 الملفات المُنشأة:")
    print("   • smart_gemini_key_manager.py")
    print("   • enhanced_description_generator.py") 
    print("   • smart_gemini_integration.py")
    print("   • INSTALLATION_GUIDE.md")
    print("\n📖 اقرأ ملف INSTALLATION_GUIDE.md للتعليمات التفصيلية")
    print("🔧 بعد التثبيت، ستحصل على:")
    print("   ✅ إدارة ذكية لـ 66 مفتاح API")
    print("   ✅ أوصاف متكاملة وعالية الجودة")
    print("   ✅ تبديل تلقائي عند المشاكل")
    print("   ✅ إحصائيات ومراقبة متقدمة")

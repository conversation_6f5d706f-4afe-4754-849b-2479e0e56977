# دليل تثبيت النظام الذكي لإدارة مفاتيح Gemini

## 📋 الخطوات المطلوبة:

### 1. نسخ الملفات الجديدة
تأكد من وجود الملفات التالية في نفس مجلد الأداة:
- `smart_gemini_key_manager.py`
- `enhanced_description_generator.py`
- `smart_gemini_integration.py`

### 2. تحديث الملف الرئيسي
أضف السطر التالي في بداية ملف `mod_processor_broken_final.py` (بعد الاستيرادات):

```python
# استيراد النظام الذكي لإدارة مفاتيح Gemini
exec(open('smart_gemini_integration.py', encoding='utf-8').read())
```

### 3. تحديث دوال إنشاء الأوصاف
استبدل الدوال التالية في `mod_processor_broken_final.py`:

#### استبدال `generate_description_task`:
```python
# استبدل السطر:
# run_in_thread(generate_description_task, mod_name, mod_category, scraped_context, manual_features_text)
# بـ:
run_in_thread(enhanced_generate_description_task, mod_name, mod_category, scraped_context, manual_features_text)
```

#### استبدال `generate_arabic_description_task`:
```python
# استبدل السطر:
# run_in_thread(generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)
# بـ:
run_in_thread(enhanced_generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)
```

### 4. تهيئة النظام
أضف السطر التالي في دالة `main()` أو في بداية التطبيق:

```python
# تهيئة النظام الذكي
initialize_smart_gemini_system()
```

### 5. إضافة زر الإحصائيات (اختياري)
أضف السطر التالي في مكان مناسب لإضافة زر الإحصائيات:

```python
add_smart_gemini_stats_button()
```

## 🎯 الميزات الجديدة:

### ✅ إدارة ذكية للمفاتيح:
- تبديل تلقائي عند فشل المفتاح
- تتبع حالة كل مفتاح
- فترات تبريد ذكية
- توزيع الحمل بين المفاتيح

### ✅ أوصاف محسنة:
- أوصاف أطول وأكثر تفصيلاً
- جودة أفضل باللغتين
- إعادة المحاولة الذكية
- تحليل محتوى المود

### ✅ مراقبة وإحصائيات:
- تتبع أداء كل مفتاح
- إحصائيات مفصلة
- تقارير صحة المفاتيح
- تصدير البيانات

## 🔧 استكشاف الأخطاء:

### إذا لم يعمل النظام:
1. تأكد من وجود جميع الملفات
2. تحقق من صحة مفاتيح API في `api_keys.json`
3. راجع رسائل الخطأ في وحدة التحكم
4. استخدم زر "اختبار جميع المفاتيح" للتشخيص

### للعودة للنظام القديم:
1. احذف أو علق على سطر استيراد `smart_gemini_integration.py`
2. أعد الدوال الأصلية
3. أعد تشغيل الأداة

## 📞 الدعم:
إذا واجهت أي مشاكل، تحقق من:
- ملف `gemini_stats.json` للإحصائيات
- رسائل وحدة التحكم للأخطاء
- حالة مفاتيح API في لوحة Google AI Studio

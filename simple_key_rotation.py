# -*- coding: utf-8 -*-
"""
نظام بسيط لتوزيع مفاتيح Gemini API
يضمن استخدام مفاتيح مختلفة للاستخراج والأوصاف
"""

import json
import random
import time
from typing import List, Optional

class SimpleKeyRotator:
    """مدير بسيط لتوزيع مفاتيح API"""
    
    def __init__(self, config_file: str = "api_keys.json"):
        self.config_file = config_file
        self.keys: List[str] = []
        self.extraction_key_index = 0
        self.description_key_index = 0
        self.last_used_times = {}
        
        self.load_keys()
        self.setup_key_distribution()
    
    def load_keys(self) -> bool:
        """تحميل مفاتيح API من ملف الإعدادات"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # تحميل المفاتيح من مصادر مختلفة
            api_keys = []
            
            # المفتاح الفردي
            single_key = config.get("GEMINI_API_KEY")
            if single_key and single_key.strip() and single_key != "YOUR_GEMINI_KEY_HERE":
                api_keys.append(single_key.strip())
            
            # قائمة المفاتيح
            keys_list = config.get("gemini_api_keys", [])
            for key in keys_list:
                if key and key.strip() and key != "YOUR_GEMINI_KEY_HERE" and key not in api_keys:
                    api_keys.append(key.strip())
            
            self.keys = api_keys
            print(f"🔑 تم تحميل {len(self.keys)} مفتاح API")
            return len(self.keys) > 0
            
        except Exception as e:
            print(f"❌ خطأ في تحميل مفاتيح API: {e}")
            return False
    
    def setup_key_distribution(self):
        """إعداد توزيع المفاتيح"""
        if len(self.keys) < 2:
            print("⚠️ يُنصح بوجود مفتاحين على الأقل للتوزيع الأمثل")
            self.extraction_key_index = 0
            self.description_key_index = 0
        else:
            # استخدام مفاتيح مختلفة للاستخراج والأوصاف
            self.extraction_key_index = 0
            self.description_key_index = len(self.keys) // 2  # استخدام مفتاح من النصف الثاني
            
        print(f"📊 توزيع المفاتيح:")
        print(f"   • الاستخراج: المفتاح {self.extraction_key_index + 1}")
        print(f"   • الأوصاف: المفتاح {self.description_key_index + 1}")
    
    def get_extraction_key(self) -> Optional[str]:
        """الحصول على مفتاح للاستخراج"""
        if not self.keys:
            return None
        
        # تسجيل وقت الاستخدام
        current_time = time.time()
        self.last_used_times[f"extraction_{self.extraction_key_index}"] = current_time
        
        key = self.keys[self.extraction_key_index]
        print(f"🔍 استخدام مفتاح الاستخراج {self.extraction_key_index + 1}")
        
        return key
    
    def get_description_key(self) -> Optional[str]:
        """الحصول على مفتاح للأوصاف"""
        if not self.keys:
            return None
        
        # تسجيل وقت الاستخدام
        current_time = time.time()
        self.last_used_times[f"description_{self.description_key_index}"] = current_time
        
        key = self.keys[self.description_key_index]
        print(f"📝 استخدام مفتاح الأوصاف {self.description_key_index + 1}")
        
        return key
    
    def rotate_extraction_key(self):
        """تبديل مفتاح الاستخراج للمفتاح التالي"""
        if len(self.keys) <= 1:
            return
        
        old_index = self.extraction_key_index
        self.extraction_key_index = (self.extraction_key_index + 1) % len(self.keys)
        
        # تجنب استخدام نفس مفتاح الأوصاف
        if self.extraction_key_index == self.description_key_index and len(self.keys) > 2:
            self.extraction_key_index = (self.extraction_key_index + 1) % len(self.keys)
        
        print(f"🔄 تبديل مفتاح الاستخراج من {old_index + 1} إلى {self.extraction_key_index + 1}")
    
    def rotate_description_key(self):
        """تبديل مفتاح الأوصاف للمفتاح التالي"""
        if len(self.keys) <= 1:
            return
        
        old_index = self.description_key_index
        self.description_key_index = (self.description_key_index + 1) % len(self.keys)
        
        # تجنب استخدام نفس مفتاح الاستخراج
        if self.description_key_index == self.extraction_key_index and len(self.keys) > 2:
            self.description_key_index = (self.description_key_index + 1) % len(self.keys)
        
        print(f"🔄 تبديل مفتاح الأوصاف من {old_index + 1} إلى {self.description_key_index + 1}")
    
    def get_random_key(self) -> Optional[str]:
        """الحصول على مفتاح عشوائي"""
        if not self.keys:
            return None
        
        random_index = random.randint(0, len(self.keys) - 1)
        key = self.keys[random_index]
        print(f"🎲 استخدام مفتاح عشوائي {random_index + 1}")
        
        return key
    
    def mark_key_error(self, key_type: str):
        """تسجيل خطأ في مفتاح وتبديله"""
        if key_type == "extraction":
            print(f"❌ خطأ في مفتاح الاستخراج {self.extraction_key_index + 1}")
            self.rotate_extraction_key()
        elif key_type == "description":
            print(f"❌ خطأ في مفتاح الأوصاف {self.description_key_index + 1}")
            self.rotate_description_key()
    
    def get_stats(self) -> dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            "total_keys": len(self.keys),
            "extraction_key": self.extraction_key_index + 1,
            "description_key": self.description_key_index + 1,
            "last_used_times": self.last_used_times
        }

# مثيل عام للاستخدام
key_rotator = None

def initialize_key_rotator() -> SimpleKeyRotator:
    """تهيئة مدير المفاتيح العام"""
    global key_rotator
    if key_rotator is None:
        key_rotator = SimpleKeyRotator()
    return key_rotator

def get_key_rotator() -> Optional[SimpleKeyRotator]:
    """الحصول على مدير المفاتيح"""
    return key_rotator

# دوال مساعدة للاستخدام السهل
def get_extraction_key() -> Optional[str]:
    """دالة مساعدة للحصول على مفتاح الاستخراج"""
    rotator = get_key_rotator()
    if rotator:
        return rotator.get_extraction_key()
    return None

def get_description_key() -> Optional[str]:
    """دالة مساعدة للحصول على مفتاح الأوصاف"""
    rotator = get_key_rotator()
    if rotator:
        return rotator.get_description_key()
    return None

def get_random_key() -> Optional[str]:
    """دالة مساعدة للحصول على مفتاح عشوائي"""
    rotator = get_key_rotator()
    if rotator:
        return rotator.get_random_key()
    return None

def mark_extraction_key_error():
    """دالة مساعدة لتسجيل خطأ في مفتاح الاستخراج"""
    rotator = get_key_rotator()
    if rotator:
        rotator.mark_key_error("extraction")

def mark_description_key_error():
    """دالة مساعدة لتسجيل خطأ في مفتاح الأوصاف"""
    rotator = get_key_rotator()
    if rotator:
        rotator.mark_key_error("description")

def get_key_stats() -> dict:
    """دالة مساعدة للحصول على إحصائيات المفاتيح"""
    rotator = get_key_rotator()
    if rotator:
        return rotator.get_stats()
    return {}

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار نظام توزيع المفاتيح")
    
    rotator = SimpleKeyRotator()
    
    # اختبار الحصول على مفاتيح
    print("\n🔍 اختبار مفاتيح الاستخراج:")
    for i in range(3):
        key = rotator.get_extraction_key()
        if key:
            print(f"   مفتاح {i+1}: {key[:12]}...")
        rotator.rotate_extraction_key()
    
    print("\n📝 اختبار مفاتيح الأوصاف:")
    for i in range(3):
        key = rotator.get_description_key()
        if key:
            print(f"   مفتاح {i+1}: {key[:12]}...")
        rotator.rotate_description_key()
    
    # طباعة الإحصائيات
    stats = rotator.get_stats()
    print(f"\n📊 الإحصائيات: {stats}")
    
    print("✅ انتهى الاختبار")

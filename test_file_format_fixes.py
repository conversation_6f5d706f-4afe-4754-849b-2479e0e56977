# -*- coding: utf-8 -*-
"""
ملف اختبار للتأكد من عمل إصلاحات صيغ الملفات
"""

import os
import zipfile
import json
from io import BytesIO

def create_test_mcpack_file(filename, mod_name="Test Mod"):
    """إنشاء ملف mcpack تجريبي"""
    
    # إنشاء محتوى manifest.json
    manifest = {
        "format_version": 2,
        "header": {
            "description": f"Test {mod_name}",
            "name": mod_name,
            "uuid": "12345678-1234-1234-1234-123456789012",
            "version": [1, 0, 0],
            "min_engine_version": [1, 16, 0]
        },
        "modules": [
            {
                "description": "Test Module",
                "type": "resources",
                "uuid": "*************-4321-4321-************",
                "version": [1, 0, 0]
            }
        ]
    }
    
    # إنشاء ملف mcpack
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.writestr("manifest.json", json.dumps(manifest, indent=2))
        zipf.writestr("pack_icon.png", b"PNG fake content for testing")
        zipf.writestr("README.txt", f"This is a test {mod_name} file.")
    
    print(f"✅ تم إنشاء ملف اختبار: {filename}")

def create_test_mcaddon_file(filename, mod_name="Test Addon"):
    """إنشاء ملف mcaddon تجريبي"""
    
    # إنشاء ملف BP
    bp_manifest = {
        "format_version": 2,
        "header": {
            "description": f"Test {mod_name} BP",
            "name": f"{mod_name} BP",
            "uuid": "11111111-1111-1111-1111-111111111111",
            "version": [1, 0, 0],
            "min_engine_version": [1, 16, 0]
        },
        "modules": [
            {
                "description": "Behavior Pack Module",
                "type": "data",
                "uuid": "*************-2222-2222-************",
                "version": [1, 0, 0]
            }
        ]
    }
    
    # إنشاء ملف RP
    rp_manifest = {
        "format_version": 2,
        "header": {
            "description": f"Test {mod_name} RP",
            "name": f"{mod_name} RP",
            "uuid": "*************-3333-3333-************",
            "version": [1, 0, 0],
            "min_engine_version": [1, 16, 0]
        },
        "modules": [
            {
                "description": "Resource Pack Module",
                "type": "resources",
                "uuid": "*************-4444-4444-************",
                "version": [1, 0, 0]
            }
        ]
    }
    
    # إنشاء ملفات mcpack منفصلة في الذاكرة
    bp_buffer = BytesIO()
    with zipfile.ZipFile(bp_buffer, 'w', zipfile.ZIP_DEFLATED) as bp_zip:
        bp_zip.writestr("manifest.json", json.dumps(bp_manifest, indent=2))
        bp_zip.writestr("pack_icon.png", b"BP PNG content")
    
    rp_buffer = BytesIO()
    with zipfile.ZipFile(rp_buffer, 'w', zipfile.ZIP_DEFLATED) as rp_zip:
        rp_zip.writestr("manifest.json", json.dumps(rp_manifest, indent=2))
        rp_zip.writestr("pack_icon.png", b"RP PNG content")
    
    # إنشاء ملف mcaddon يحتوي على الملفين
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.writestr(f"{mod_name}_BP.mcpack", bp_buffer.getvalue())
        zipf.writestr(f"{mod_name}_RP.mcpack", rp_buffer.getvalue())
    
    print(f"✅ تم إنشاء ملف اختبار: {filename}")

def create_test_zip_file(filename, mod_name="Test Zip"):
    """إنشاء ملف zip عادي للاختبار"""
    
    manifest = {
        "format_version": 2,
        "header": {
            "description": f"Test {mod_name}",
            "name": mod_name,
            "uuid": "*************-5555-5555-************",
            "version": [1, 0, 0],
            "min_engine_version": [1, 16, 0]
        },
        "modules": [
            {
                "description": "Test Module",
                "type": "resources",
                "uuid": "*************-6666-6666-************",
                "version": [1, 0, 0]
            }
        ]
    }
    
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.writestr("manifest.json", json.dumps(manifest, indent=2))
        zipf.writestr("pack_icon.png", b"ZIP PNG content")
        zipf.writestr("README.txt", f"This is a test {mod_name} file in ZIP format.")
    
    print(f"✅ تم إنشاء ملف اختبار: {filename}")

def create_test_files():
    """إنشاء جميع ملفات الاختبار"""
    
    print("🔧 إنشاء ملفات اختبار لصيغ الملفات...")
    
    # إنشاء مجلد للاختبارات
    test_dir = "test_files"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # إنشاء ملفات اختبار مختلفة
    create_test_mcpack_file(os.path.join(test_dir, "test_resource_pack.mcpack"), "Test Resource Pack")
    create_test_mcpack_file(os.path.join(test_dir, "test_behavior_pack.mcpack"), "Test Behavior Pack")
    create_test_mcaddon_file(os.path.join(test_dir, "test_addon.mcaddon"), "Test Addon")
    create_test_zip_file(os.path.join(test_dir, "test_mod.zip"), "Test Mod")
    
    # إنشاء ملف بصيغة غير معروفة
    with open(os.path.join(test_dir, "test_unknown.dat"), 'w') as f:
        f.write("This is a test file with unknown extension")
    
    print(f"\n✅ تم إنشاء جميع ملفات الاختبار في مجلد: {test_dir}")
    print("\nملفات الاختبار:")
    for file in os.listdir(test_dir):
        file_path = os.path.join(test_dir, file)
        size = os.path.getsize(file_path)
        print(f"  - {file} ({size} bytes)")

def test_file_format_detection():
    """اختبار كشف صيغ الملفات"""
    
    print("\n🔍 اختبار كشف صيغ الملفات...")
    
    test_dir = "test_files"
    if not os.path.exists(test_dir):
        print("❌ مجلد الاختبار غير موجود. قم بتشغيل create_test_files() أولاً.")
        return
    
    for filename in os.listdir(test_dir):
        file_path = os.path.join(test_dir, filename)
        ext = os.path.splitext(filename)[1].lower()
        
        print(f"\n📁 فحص الملف: {filename}")
        print(f"   الصيغة: {ext}")
        
        if ext in ['.mcpack', '.mcaddon']:
            print(f"   ✅ صيغة مود معروفة")
            
            # فحص محتوى الملف
            try:
                with zipfile.ZipFile(file_path, 'r') as zipf:
                    files_in_zip = zipf.namelist()
                    manifest_files = [f for f in files_in_zip if f.endswith('manifest.json')]
                    mcpack_files = [f for f in files_in_zip if f.lower().endswith('.mcpack')]
                    
                    print(f"   📋 ملفات manifest: {len(manifest_files)}")
                    print(f"   📦 ملفات mcpack بالداخل: {len(mcpack_files)}")
                    
                    if ext == '.mcaddon' and mcpack_files:
                        print(f"   ✅ mcaddon صحيح (يحتوي على ملفات mcpack)")
                    elif ext == '.mcpack' and manifest_files and not mcpack_files:
                        print(f"   ✅ mcpack صحيح (يحتوي على manifest مباشر)")
                    else:
                        print(f"   ⚠️ قد تكون هناك مشكلة في البنية")
                        
            except Exception as e:
                print(f"   ❌ خطأ في فحص الملف: {e}")
                
        elif ext == '.zip':
            print(f"   🔍 ملف ZIP - يحتاج فحص المحتوى")
            
            try:
                with zipfile.ZipFile(file_path, 'r') as zipf:
                    files_in_zip = zipf.namelist()
                    manifest_files = [f for f in files_in_zip if f.endswith('manifest.json')]
                    mcpack_files = [f for f in files_in_zip if f.lower().endswith('.mcpack')]
                    
                    if manifest_files and not mcpack_files:
                        print(f"   💡 يبدو أنه mcpack (يحتوي على manifest)")
                    elif mcpack_files:
                        print(f"   💡 يبدو أنه mcaddon (يحتوي على ملفات mcpack)")
                    else:
                        print(f"   ❓ ملف ZIP عادي")
                        
            except Exception as e:
                print(f"   ❌ خطأ في فحص الملف: {e}")
                
        else:
            print(f"   ❓ صيغة غير معروفة")

if __name__ == "__main__":
    print("🧪 اختبار إصلاحات صيغ الملفات")
    print("=" * 50)
    
    # إنشاء ملفات الاختبار
    create_test_files()
    
    # اختبار كشف الصيغ
    test_file_format_detection()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
    print("\nيمكنك الآن اختبار الأداة الرئيسية باستخدام الملفات في مجلد test_files")

# -*- coding: utf-8 -*-
"""
مولد أوصاف محسن باستخدام النظام الذكي لإدارة مفاتيح Gemini API
يضمن إنشاء أوصاف متكاملة وعالية الجودة باللغتين العربية والإنجليزية
"""

import json
import time
import re
from typing import Dict, Tuple, Optional, Any
from smart_gemini_key_manager import SmartGeminiKeyManager, initialize_gemini_manager

class EnhancedDescriptionGenerator:
    """مولد أوصاف محسن مع نظام ذكي لإدارة المفاتيح"""
    
    def __init__(self):
        self.manager = initialize_gemini_manager()
        self.min_description_length = 200  # الحد الأدنى لطول الوصف
        self.max_retries = 5  # عدد المحاولات القصوى
        
        # قوالب الأوصاف المحسنة
        self.english_template = """
Create a comprehensive and engaging English description for this Minecraft addon/mod:

**Mod Information:**
- Name: {name}
- Category: {category}
- Creator: {creator}

**Content Analysis:**
{content_summary}

**Requirements:**
1. Write a detailed description (minimum 300 words)
2. Include key features and gameplay elements
3. Mention compatibility and installation notes
4. Use engaging language that appeals to Minecraft players
5. Include emojis to make it more attractive
6. Structure with clear paragraphs
7. End with a call-to-action encouraging download

**Important:** 
- Focus on what makes this mod unique and exciting
- Describe the visual and gameplay improvements
- Mention any new blocks, items, or mechanics
- Include technical details if relevant
- Make it sound professional yet exciting

Generate the description now:
"""

        self.arabic_template = """
أنشئ وصفاً عربياً شاملاً وجذاباً لهذا المود/الإضافة في ماين كرافت:

**معلومات المود:**
- الاسم: {name}
- الفئة: {category}
- المطور: {creator}

**تحليل المحتوى:**
{content_summary}

**المتطلبات:**
1. اكتب وصفاً مفصلاً (300 كلمة على الأقل)
2. اذكر الميزات الرئيسية وعناصر اللعب
3. اشر إلى التوافق وملاحظات التثبيت
4. استخدم لغة جذابة تناسب لاعبي ماين كرافت
5. أضف رموز تعبيرية لجعله أكثر جاذبية
6. نظم النص في فقرات واضحة
7. اختتم بدعوة للتحميل

**مهم:**
- ركز على ما يجعل هذا المود فريداً ومثيراً
- اوصف التحسينات البصرية وتحسينات اللعب
- اذكر أي كتل أو عناصر أو آليات جديدة
- أضف تفاصيل تقنية إذا كانت مناسبة
- اجعله يبدو احترافياً ومثيراً في نفس الوقت

أنشئ الوصف الآن:
"""

    def analyze_mod_content(self, mod_data: Dict, extracted_content: str) -> str:
        """تحليل محتوى المود لإنشاء ملخص"""
        try:
            # استخراج المعلومات الأساسية
            features = []
            
            # تحليل النص المستخرج
            if extracted_content:
                content_lower = extracted_content.lower()
                
                # البحث عن ميزات شائعة
                if any(word in content_lower for word in ['block', 'blocks', 'كتل', 'كتلة']):
                    features.append("كتل جديدة / New blocks")
                
                if any(word in content_lower for word in ['item', 'items', 'عناصر', 'عنصر']):
                    features.append("عناصر جديدة / New items")
                
                if any(word in content_lower for word in ['mob', 'mobs', 'creature', 'كائن', 'كائنات']):
                    features.append("كائنات جديدة / New mobs")
                
                if any(word in content_lower for word in ['biome', 'biomes', 'بيئة', 'بيئات']):
                    features.append("بيئات جديدة / New biomes")
                
                if any(word in content_lower for word in ['weapon', 'weapons', 'سلاح', 'أسلحة']):
                    features.append("أسلحة جديدة / New weapons")
                
                if any(word in content_lower for word in ['armor', 'armour', 'درع', 'دروع']):
                    features.append("دروع جديدة / New armor")
                
                if any(word in content_lower for word in ['recipe', 'recipes', 'وصفة', 'وصفات']):
                    features.append("وصفات تصنيع جديدة / New crafting recipes")
            
            # إنشاء ملخص
            summary = f"المود يتضمن: {', '.join(features) if features else 'ميزات متنوعة'}"
            
            # إضافة معلومات إضافية
            if mod_data.get('version'):
                summary += f"\nالإصدار: {mod_data['version']}"
            
            if mod_data.get('minecraft_version'):
                summary += f"\nمتوافق مع ماين كرافت: {mod_data['minecraft_version']}"
            
            return summary
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل المحتوى: {e}")
            return "مود ماين كرافت مع ميزات جديدة ومثيرة"

    def generate_english_description(self, mod_data: Dict, content_summary: str) -> str:
        """إنشاء وصف إنجليزي محسن"""
        try:
            # تحضير البيانات
            name = mod_data.get('name', 'This Minecraft Addon')
            category = mod_data.get('category', 'Addon')
            creator = mod_data.get('creator_name', 'Unknown Creator')
            
            # إنشاء الطلب
            prompt = self.english_template.format(
                name=name,
                category=category,
                creator=creator,
                content_summary=content_summary
            )
            
            print("🔤 إنشاء الوصف الإنجليزي...")
            
            # إرسال الطلب مع إعادة المحاولة
            for attempt in range(self.max_retries):
                response = self.manager.smart_request(prompt, max_retries=2)
                
                if response and len(response) >= self.min_description_length:
                    cleaned_desc = self.clean_english_description(response)
                    if len(cleaned_desc) >= self.min_description_length:
                        print(f"✅ تم إنشاء وصف إنجليزي ({len(cleaned_desc)} حرف)")
                        return cleaned_desc
                
                print(f"⚠️ الوصف قصير جداً، إعادة المحاولة ({attempt + 1}/{self.max_retries})")
                time.sleep(1)
            
            # إذا فشلت جميع المحاولات، استخدم وصف احتياطي محسن
            return self.get_enhanced_fallback_english(mod_data)
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف الإنجليزي: {e}")
            return self.get_enhanced_fallback_english(mod_data)

    def generate_arabic_description(self, mod_data: Dict, content_summary: str, english_desc: str = "") -> str:
        """إنشاء وصف عربي محسن"""
        try:
            # تحضير البيانات
            name = mod_data.get('name', 'هذا المود')
            category = mod_data.get('category', 'إضافة')
            creator = mod_data.get('creator_name', 'مطور غير معروف')
            
            # إضافة معلومات من الوصف الإنجليزي إذا كان متوفراً
            enhanced_summary = content_summary
            if english_desc and len(english_desc) > 100:
                enhanced_summary += f"\n\nمعلومات إضافية من الوصف الإنجليزي:\n{english_desc[:200]}..."
            
            # إنشاء الطلب
            prompt = self.arabic_template.format(
                name=name,
                category=category,
                creator=creator,
                content_summary=enhanced_summary
            )
            
            print("🔤 إنشاء الوصف العربي...")
            
            # إرسال الطلب مع إعادة المحاولة
            for attempt in range(self.max_retries):
                response = self.manager.smart_request(prompt, max_retries=2)
                
                if response and len(response) >= self.min_description_length:
                    cleaned_desc = self.clean_arabic_description(response)
                    if len(cleaned_desc) >= self.min_description_length:
                        print(f"✅ تم إنشاء وصف عربي ({len(cleaned_desc)} حرف)")
                        return cleaned_desc
                
                print(f"⚠️ الوصف قصير جداً، إعادة المحاولة ({attempt + 1}/{self.max_retries})")
                time.sleep(1)
            
            # إذا فشلت جميع المحاولات، استخدم وصف احتياطي محسن
            return self.get_enhanced_fallback_arabic(mod_data)
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف العربي: {e}")
            return self.get_enhanced_fallback_arabic(mod_data)

    def clean_english_description(self, description: str) -> str:
        """تنظيف الوصف الإنجليزي"""
        # إزالة النصوص غير المرغوبة
        unwanted_patterns = [
            r'Generate the description now:',
            r'Here\'s the description:',
            r'Description:',
            r'Here is the description:'
        ]
        
        for pattern in unwanted_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)
        
        # تنظيف المسافات والأسطر الفارغة
        description = re.sub(r'\n\s*\n', '\n\n', description)
        description = description.strip()
        
        return description

    def clean_arabic_description(self, description: str) -> str:
        """تنظيف الوصف العربي"""
        # إزالة النصوص غير المرغوبة
        unwanted_patterns = [
            r'أنشئ الوصف الآن:',
            r'الوصف:',
            r'إليك الوصف:',
            r'هذا هو الوصف:',
            r'هنا الوصف:'
        ]
        
        for pattern in unwanted_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)
        
        # تنظيف المسافات والأسطر الفارغة
        description = re.sub(r'\n\s*\n', '\n\n', description)
        description = description.strip()
        
        return description

    def get_enhanced_fallback_english(self, mod_data: Dict) -> str:
        """وصف احتياطي إنجليزي محسن"""
        name = mod_data.get('name', 'This Amazing Minecraft Addon')
        category = mod_data.get('category', 'addon')
        
        return f"""🎮 **{name}** - Transform Your Minecraft Experience! ✨

Discover the incredible world of **{name}**, a fantastic {category.lower()} that brings exciting new content to your Minecraft adventures! This carefully crafted modification introduces amazing features that will revolutionize how you play and explore.

🌟 **Key Features:**
• Stunning new content that enhances gameplay
• Carefully balanced additions that fit perfectly with vanilla Minecraft
• High-quality textures and models
• Optimized performance for smooth gameplay
• Easy installation and compatibility

🎯 **What Makes This Special:**
This {category.lower()} stands out with its attention to detail and creative design. Whether you're a casual player or a hardcore Minecraft enthusiast, you'll find something to love. The new elements blend seamlessly with the existing game while adding fresh excitement to your world.

🚀 **Perfect For:**
• Players looking for new challenges
• Builders seeking creative inspiration
• Adventurers wanting fresh content
• Anyone who loves expanding their Minecraft experience

💎 **Installation & Compatibility:**
Easy to install and compatible with the latest Minecraft versions. Simply download, import, and start enjoying the enhanced gameplay immediately!

🎉 **Ready to Transform Your World?**
Download **{name}** now and discover why thousands of players have already enhanced their Minecraft experience with this incredible {category.lower()}! Your adventure awaits! 🌍⚡"""

    def get_enhanced_fallback_arabic(self, mod_data: Dict) -> str:
        """وصف احتياطي عربي محسن"""
        name = mod_data.get('name', 'هذا المود الرائع')
        category = mod_data.get('category', 'إضافة')
        
        return f"""🎮 **{name}** - حوّل تجربة ماين كرافت الخاصة بك! ✨

اكتشف العالم المذهل لـ **{name}**، وهو {category} رائع يجلب محتوى جديد ومثير لمغامرات ماين كرافت الخاصة بك! هذا التعديل المصمم بعناية يقدم ميزات مذهلة ستحدث ثورة في طريقة لعبك واستكشافك.

🌟 **الميزات الرئيسية:**
• محتوى جديد مذهل يعزز طريقة اللعب
• إضافات متوازنة بعناية تتناسب تماماً مع ماين كرافت الأصلية
• نسيج ونماذج عالية الجودة
• أداء محسن للعب سلس
• تثبيت سهل وتوافق ممتاز

🎯 **ما يجعل هذا مميزاً:**
يتميز هذا {category} بالاهتمام بالتفاصيل والتصميم الإبداعي. سواء كنت لاعباً عادياً أو متحمساً لماين كرافت، ستجد شيئاً تحبه. العناصر الجديدة تمتزج بسلاسة مع اللعبة الموجودة بينما تضيف إثارة جديدة لعالمك.

🚀 **مثالي لـ:**
• اللاعبين الذين يبحثون عن تحديات جديدة
• البناة الذين يسعون للإلهام الإبداعي
• المغامرين الذين يريدون محتوى جديد
• أي شخص يحب توسيع تجربة ماين كرافت

💎 **التثبيت والتوافق:**
سهل التثبيت ومتوافق مع أحدث إصدارات ماين كرافت. ما عليك سوى التحميل والاستيراد والبدء في الاستمتاع بطريقة اللعب المحسنة على الفور!

🎉 **مستعد لتحويل عالمك؟**
حمّل **{name}** الآن واكتشف لماذا عزز آلاف اللاعبين بالفعل تجربة ماين كرافت الخاصة بهم مع هذا {category} المذهل! مغامرتك في انتظارك! 🌍⚡"""

    def generate_descriptions(self, mod_data: Dict, extracted_content: str = "") -> Tuple[str, str]:
        """إنشاء أوصاف متكاملة باللغتين"""
        try:
            print("🚀 بدء إنشاء الأوصاف المحسنة...")
            
            # تحليل المحتوى
            content_summary = self.analyze_mod_content(mod_data, extracted_content)
            
            # إنشاء الوصف الإنجليزي
            english_desc = self.generate_english_description(mod_data, content_summary)
            
            # إنشاء الوصف العربي (مع الاستفادة من الوصف الإنجليزي)
            arabic_desc = self.generate_arabic_description(mod_data, content_summary, english_desc)
            
            # طباعة الإحصائيات
            print(f"📊 تم إنشاء الأوصاف:")
            print(f"   • الإنجليزي: {len(english_desc)} حرف")
            print(f"   • العربي: {len(arabic_desc)} حرف")
            
            # طباعة إحصائيات المدير
            if self.manager:
                stats = self.manager.get_stats_summary()
                print(f"   • معدل نجاح المفاتيح: {stats.get('success_rate', 0)}%")
                print(f"   • مفاتيح متاحة: {stats.get('available_keys', 0)}")
            
            return english_desc, arabic_desc
            
        except Exception as e:
            print(f"❌ خطأ عام في إنشاء الأوصاف: {e}")
            return self.get_enhanced_fallback_english(mod_data), self.get_enhanced_fallback_arabic(mod_data)

# دالة مساعدة للاستخدام السهل
def generate_enhanced_descriptions(mod_data: Dict, extracted_content: str = "") -> Tuple[str, str]:
    """دالة مساعدة لإنشاء أوصاف محسنة"""
    generator = EnhancedDescriptionGenerator()
    return generator.generate_descriptions(mod_data, extracted_content)

if __name__ == "__main__":
    # اختبار المولد
    test_mod_data = {
        'name': 'DecoDrop Addon',
        'category': 'Addons',
        'creator_name': 'TestCreator',
        'version': '1.0.0'
    }
    
    test_content = "This addon adds new decorative blocks and items to Minecraft"
    
    generator = EnhancedDescriptionGenerator()
    english, arabic = generator.generate_descriptions(test_mod_data, test_content)
    
    print("\n" + "="*60)
    print("📝 الوصف الإنجليزي:")
    print("="*60)
    print(english[:200] + "...")
    
    print("\n" + "="*60)
    print("📝 الوصف العربي:")
    print("="*60)
    print(arabic[:200] + "...")

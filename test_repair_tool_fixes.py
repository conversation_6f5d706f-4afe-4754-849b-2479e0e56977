# -*- coding: utf-8 -*-
"""
اختبار بسيط للتأكد من أن إصلاحات أداة الإصلاح تعمل
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_search_function():
    """اختبار دالة البحث مع قيم None"""
    
    # محاكاة بيانات مودات مع قيم None
    test_mods = [
        {
            'id': 1,
            'name': 'Test Mod 1',
            'description': 'This is a test mod',
            'category': 'Tools'
        },
        {
            'id': 2,
            'name': 'Test Mod 2',
            'description': None,  # قيمة None
            'category': 'Weapons'
        },
        {
            'id': 3,
            'name': None,  # قيمة None
            'description': 'Another test mod',
            'category': None  # قيمة None
        }
    ]
    
    search_term = "test"
    
    print("🧪 اختبار دالة البحث مع قيم None...")
    
    for mod in test_mods:
        print(f"\n📋 فحص المود ID: {mod.get('id')}")
        
        try:
            # محاكاة منطق البحث المُصلح
            name_match = search_term in (mod.get('name') or '').lower()
            desc_match = search_term in (mod.get('description') or '').lower()
            cat_match = search_term in (mod.get('category') or '').lower()
            
            if name_match or desc_match or cat_match:
                print(f"  ✅ تطابق موجود")
                print(f"     الاسم: {mod.get('name', 'بدون اسم')}")
                print(f"     الوصف: {mod.get('description', 'بدون وصف')}")
                print(f"     الفئة: {mod.get('category', 'بدون فئة')}")
            else:
                print(f"  ❌ لا يوجد تطابق")
                
        except Exception as e:
            print(f"  💥 خطأ: {e}")

def test_file_format_functions():
    """اختبار دوال صيغ الملفات"""
    
    print("\n🔧 اختبار دوال صيغ الملفات...")
    
    # اختبار تحديد صيغة الملف من الرابط
    test_urls = [
        "https://example.com/mod.mcpack",
        "https://example.com/mod.mcaddon", 
        "https://example.com/mod.zip",
        "https://example.com/mod.unknown",
        "https://example.com/mod.mcpack.mcaddon",  # صيغة مكررة
    ]
    
    for url in test_urls:
        print(f"\n📁 فحص الرابط: {url}")
        
        # استخراج الصيغة
        if '.mcpack' in url and '.mcaddon' in url:
            print("  ⚠️ صيغة مكررة - يحتاج إصلاح")
        elif url.endswith('.mcpack'):
            print("  ✅ صيغة mcpack")
        elif url.endswith('.mcaddon'):
            print("  ✅ صيغة mcaddon")
        elif url.endswith('.zip'):
            print("  🔍 ملف ZIP - يحتاج فحص المحتوى")
        else:
            print("  ❓ صيغة غير معروفة")

def test_mod_format_change():
    """اختبار تغيير صيغة المود"""
    
    print("\n🔄 اختبار تغيير صيغة المود...")
    
    # محاكاة مود
    test_mod = {
        'id': 123,
        'name': 'Test Mod',
        'category': 'Tools',
        'download_url': 'https://example.com/test_mod.mcpack'
    }
    
    print(f"المود الأصلي:")
    print(f"  الاسم: {test_mod['name']}")
    print(f"  الرابط: {test_mod['download_url']}")
    
    # محاكاة تغيير الصيغة
    current_url = test_mod['download_url']
    new_format = "mcaddon"
    
    # استبدال الصيغة
    new_url = current_url
    for old_ext in ['.mcpack', '.mcaddon', '.zip']:
        if old_ext in new_url:
            new_url = new_url.replace(old_ext, f'.{new_format}')
            break
    
    print(f"\nبعد التغيير:")
    print(f"  الرابط الجديد: {new_url}")
    print(f"  ✅ تم تغيير الصيغة بنجاح")

def test_extension_detection():
    """اختبار كشف المشاكل في الصيغ"""
    
    print("\n🔍 اختبار كشف مشاكل الصيغ...")
    
    # محاكاة مودات مع مشاكل مختلفة
    test_mods = [
        {
            'id': 1,
            'name': 'Normal Mod',
            'download_url': 'https://example.com/normal.mcpack'
        },
        {
            'id': 2, 
            'name': 'Duplicate Extension Mod',
            'download_url': 'https://example.com/duplicate.mcpack.mcaddon'
        },
        {
            'id': 3,
            'name': 'Addon Pack',
            'download_url': 'https://example.com/addon.mcpack'  # يجب أن يكون mcaddon
        },
        {
            'id': 4,
            'name': 'Resource Pack',
            'download_url': 'https://example.com/resource.mcaddon'  # يجب أن يكون mcpack
        }
    ]
    
    mods_needing_fix = []
    
    for mod in test_mods:
        mod_url = mod.get('download_url', '')
        mod_name = mod.get('name', '')
        
        print(f"\n📋 فحص المود: {mod_name}")
        print(f"   الرابط: {mod_url}")
        
        if '.mcpack' in mod_url and '.mcaddon' in mod_url:
            print("   ⚠️ مشكلة: صيغة مكررة")
            mods_needing_fix.append({
                'mod': mod,
                'issue': 'duplicate_extensions'
            })
        elif mod_url.endswith('.mcpack') and 'addon' in mod_name.lower():
            print("   ⚠️ مشكلة: يجب أن يكون mcaddon")
            mods_needing_fix.append({
                'mod': mod,
                'issue': 'wrong_extension_mcpack'
            })
        elif mod_url.endswith('.mcaddon') and 'pack' in mod_name.lower() and 'addon' not in mod_name.lower():
            print("   ⚠️ مشكلة: يجب أن يكون mcpack")
            mods_needing_fix.append({
                'mod': mod,
                'issue': 'wrong_extension_mcaddon'
            })
        else:
            print("   ✅ لا توجد مشاكل")
    
    print(f"\n📊 النتائج:")
    print(f"   إجمالي المودات: {len(test_mods)}")
    print(f"   تحتاج إصلاح: {len(mods_needing_fix)}")
    print(f"   سليمة: {len(test_mods) - len(mods_needing_fix)}")

if __name__ == "__main__":
    print("🧪 اختبار إصلاحات أداة الإصلاح")
    print("=" * 50)
    
    # تشغيل الاختبارات
    test_search_function()
    test_file_format_functions()
    test_mod_format_change()
    test_extension_detection()
    
    print("\n" + "=" * 50)
    print("✅ انتهت جميع الاختبارات")
    print("\nالإصلاحات المطبقة:")
    print("1. ✅ إصلاح مشكلة البحث مع قيم None")
    print("2. ✅ إضافة ميزة تغيير صيغة المود")
    print("3. ✅ إضافة كشف مشاكل الصيغ")
    print("4. ✅ إضافة دوال الإصلاح المفقودة")

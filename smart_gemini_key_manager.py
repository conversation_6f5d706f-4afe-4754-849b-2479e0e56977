# -*- coding: utf-8 -*-
"""
نظام ذكي ومتقدم لإدارة مفاتيح Gemini API
يتضمن: rotation ذكي، تتبع حالة المفاتيح، cooldown periods، load balancing، وإحصائيات الاستخدام
"""

import json
import time
import random
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import google.generativeai as genai

class KeyStatus(Enum):
    """حالات مفاتيح API"""
    ACTIVE = "active"           # نشط ومتاح
    QUOTA_EXCEEDED = "quota"    # تم تجاوز الحد المسموح
    INVALID = "invalid"         # مفتاح غير صالح
    RATE_LIMITED = "rate_limit" # محدود بمعدل الطلبات
    COOLDOWN = "cooldown"       # في فترة تبريد
    ERROR = "error"             # خطأ عام
    UNKNOWN = "unknown"         # حالة غير معروفة

@dataclass
class KeyInfo:
    """معلومات مفتاح API"""
    key: str
    status: KeyStatus = KeyStatus.UNKNOWN
    last_used: Optional[datetime] = None
    last_error: Optional[str] = None
    error_count: int = 0
    success_count: int = 0
    cooldown_until: Optional[datetime] = None
    total_requests: int = 0
    avg_response_time: float = 0.0
    last_response_time: float = 0.0
    priority: int = 1  # أولوية المفتاح (1 = عالية، 5 = منخفضة)

class SmartGeminiKeyManager:
    """مدير ذكي لمفاتيح Gemini API"""
    
    def __init__(self, config_file: str = "api_keys.json", stats_file: str = "gemini_stats.json"):
        self.config_file = config_file
        self.stats_file = stats_file
        self.keys: List[KeyInfo] = []
        self.current_key_index = 0
        self.model = None
        self.lock = threading.Lock()
        
        # إعدادات النظام
        self.cooldown_periods = {
            KeyStatus.QUOTA_EXCEEDED: timedelta(hours=1),
            KeyStatus.RATE_LIMITED: timedelta(minutes=15),
            KeyStatus.ERROR: timedelta(minutes=5),
            KeyStatus.INVALID: timedelta(days=1)
        }
        
        self.max_retries_per_key = 3
        self.model_name = "gemini-1.5-flash"
        
        # تحميل المفاتيح والإحصائيات
        self.load_keys()
        self.load_stats()
        
        print(f"🔑 تم تهيئة مدير مفاتيح Gemini مع {len(self.keys)} مفتاح")

    def load_keys(self) -> bool:
        """تحميل مفاتيح API من ملف الإعدادات"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # تحميل المفاتيح من مصادر مختلفة
            api_keys = []
            
            # المفتاح الفردي
            single_key = config.get("GEMINI_API_KEY")
            if single_key and single_key.strip() and single_key != "YOUR_GEMINI_KEY_HERE":
                api_keys.append(single_key.strip())
            
            # قائمة المفاتيح
            keys_list = config.get("gemini_api_keys", [])
            for key in keys_list:
                if key and key.strip() and key != "YOUR_GEMINI_KEY_HERE" and key not in api_keys:
                    api_keys.append(key.strip())
            
            # إنشاء كائنات KeyInfo
            self.keys = []
            for i, key in enumerate(api_keys):
                key_info = KeyInfo(
                    key=key,
                    priority=1 if i < 10 else 2 if i < 30 else 3  # أولوية حسب الترتيب
                )
                self.keys.append(key_info)
            
            print(f"✅ تم تحميل {len(self.keys)} مفتاح Gemini")
            return len(self.keys) > 0
            
        except Exception as e:
            print(f"❌ خطأ في تحميل مفاتيح API: {e}")
            return False

    def load_stats(self) -> bool:
        """تحميل إحصائيات المفاتيح المحفوظة"""
        try:
            with open(self.stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            
            # تطبيق الإحصائيات على المفاتيح
            for key_info in self.keys:
                key_stats = stats.get(key_info.key[:20], {})  # استخدام أول 20 حرف كمعرف
                
                if key_stats:
                    key_info.error_count = key_stats.get('error_count', 0)
                    key_info.success_count = key_stats.get('success_count', 0)
                    key_info.total_requests = key_stats.get('total_requests', 0)
                    key_info.avg_response_time = key_stats.get('avg_response_time', 0.0)
                    
                    # تحويل التواريخ
                    if key_stats.get('last_used'):
                        key_info.last_used = datetime.fromisoformat(key_stats['last_used'])
                    if key_stats.get('cooldown_until'):
                        key_info.cooldown_until = datetime.fromisoformat(key_stats['cooldown_until'])
            
            print(f"✅ تم تحميل إحصائيات المفاتيح")
            return True
            
        except FileNotFoundError:
            print("📊 لا توجد إحصائيات محفوظة، سيتم إنشاء ملف جديد")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في تحميل الإحصائيات: {e}")
            return True

    def save_stats(self) -> bool:
        """حفظ إحصائيات المفاتيح"""
        try:
            stats = {}
            for key_info in self.keys:
                key_id = key_info.key[:20]  # استخدام أول 20 حرف كمعرف
                stats[key_id] = {
                    'error_count': key_info.error_count,
                    'success_count': key_info.success_count,
                    'total_requests': key_info.total_requests,
                    'avg_response_time': key_info.avg_response_time,
                    'last_used': key_info.last_used.isoformat() if key_info.last_used else None,
                    'cooldown_until': key_info.cooldown_until.isoformat() if key_info.cooldown_until else None,
                    'status': key_info.status.value,
                    'priority': key_info.priority
                }
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الإحصائيات: {e}")
            return False

    def get_available_keys(self) -> List[Tuple[int, KeyInfo]]:
        """الحصول على المفاتيح المتاحة مرتبة حسب الأولوية"""
        available = []
        current_time = datetime.now()
        
        for i, key_info in enumerate(self.keys):
            # تحقق من فترة التبريد
            if key_info.cooldown_until and current_time < key_info.cooldown_until:
                continue
            
            # تجاهل المفاتيح غير الصالحة
            if key_info.status == KeyStatus.INVALID:
                continue
            
            available.append((i, key_info))
        
        # ترتيب حسب الأولوية ومعدل النجاح
        available.sort(key=lambda x: (
            x[1].priority,  # أولوية أقل = أفضل
            -x[1].success_count,  # نجاحات أكثر = أفضل
            x[1].error_count,  # أخطاء أقل = أفضل
            x[1].avg_response_time  # وقت استجابة أقل = أفضل
        ))
        
        return available

    def get_best_key(self) -> Optional[Tuple[int, KeyInfo]]:
        """الحصول على أفضل مفتاح متاح"""
        available_keys = self.get_available_keys()
        
        if not available_keys:
            return None
        
        # إضافة عشوائية للمفاتيح عالية الأولوية لتوزيع الحمل
        top_keys = [k for k in available_keys if k[1].priority <= 2]
        if len(top_keys) > 1:
            return random.choice(top_keys[:min(3, len(top_keys))])
        
        return available_keys[0]

    def configure_client(self, key_index: int) -> bool:
        """تكوين عميل Gemini بمفتاح محدد"""
        try:
            if key_index >= len(self.keys):
                return False
            
            key_info = self.keys[key_index]
            
            genai.configure(api_key=key_info.key)
            self.model = genai.GenerativeModel(self.model_name)
            self.current_key_index = key_index
            
            print(f"🔧 تم تكوين Gemini بالمفتاح {key_index + 1}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تكوين العميل: {e}")
            return False

    def mark_key_error(self, key_index: int, error: str, status: KeyStatus = KeyStatus.ERROR):
        """تسجيل خطأ في مفتاح"""
        if key_index >= len(self.keys):
            return
        
        with self.lock:
            key_info = self.keys[key_index]
            key_info.error_count += 1
            key_info.last_error = error
            key_info.status = status
            key_info.total_requests += 1
            
            # تحديد فترة التبريد
            cooldown_period = self.cooldown_periods.get(status, timedelta(minutes=5))
            key_info.cooldown_until = datetime.now() + cooldown_period
            
            print(f"⚠️ خطأ في المفتاح {key_index + 1}: {error}")
            print(f"🕒 فترة تبريد حتى: {key_info.cooldown_until.strftime('%H:%M:%S')}")
            
            self.save_stats()

    def mark_key_success(self, key_index: int, response_time: float):
        """تسجيل نجاح في مفتاح"""
        if key_index >= len(self.keys):
            return
        
        with self.lock:
            key_info = self.keys[key_index]
            key_info.success_count += 1
            key_info.total_requests += 1
            key_info.last_used = datetime.now()
            key_info.status = KeyStatus.ACTIVE
            key_info.last_response_time = response_time
            
            # تحديث متوسط وقت الاستجابة
            if key_info.avg_response_time == 0:
                key_info.avg_response_time = response_time
            else:
                key_info.avg_response_time = (key_info.avg_response_time + response_time) / 2
            
            # إزالة فترة التبريد عند النجاح
            key_info.cooldown_until = None
            
            self.save_stats()

    def smart_request(self, prompt: str, max_retries: int = 3) -> Optional[str]:
        """طلب ذكي مع تبديل تلقائي للمفاتيح"""
        if not self.keys:
            print("❌ لا توجد مفاتيح API متاحة")
            return None
        
        keys_tried = set()
        
        for attempt in range(max_retries):
            # الحصول على أفضل مفتاح متاح
            best_key = self.get_best_key()
            if not best_key:
                print("❌ لا توجد مفاتيح متاحة حالياً")
                break
            
            key_index, key_info = best_key
            
            # تجنب تكرار نفس المفتاح
            if key_index in keys_tried:
                continue
            
            keys_tried.add(key_index)
            
            # تكوين العميل
            if not self.configure_client(key_index):
                continue
            
            # تنفيذ الطلب
            start_time = time.time()
            try:
                print(f"📤 إرسال طلب باستخدام المفتاح {key_index + 1} (المحاولة {attempt + 1})")
                
                response = self.model.generate_content(prompt)
                
                if response and response.text:
                    response_time = time.time() - start_time
                    self.mark_key_success(key_index, response_time)
                    
                    print(f"✅ نجح الطلب في {response_time:.2f} ثانية")
                    return response.text.strip()
                else:
                    raise Exception("استجابة فارغة من Gemini")
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                # تحديد نوع الخطأ
                if any(keyword in error_msg for keyword in ["quota", "rate_limit", "resource_exhausted", "429"]):
                    self.mark_key_error(key_index, str(e), KeyStatus.QUOTA_EXCEEDED)
                elif any(keyword in error_msg for keyword in ["api_key", "invalid", "permission", "401", "403"]):
                    self.mark_key_error(key_index, str(e), KeyStatus.INVALID)
                elif any(keyword in error_msg for keyword in ["rate", "limit", "too many"]):
                    self.mark_key_error(key_index, str(e), KeyStatus.RATE_LIMITED)
                else:
                    self.mark_key_error(key_index, str(e), KeyStatus.ERROR)
                
                print(f"❌ فشل المفتاح {key_index + 1}: {e}")
                
                # انتظار قصير قبل المحاولة التالية
                if attempt < max_retries - 1:
                    time.sleep(1)
        
        print(f"❌ فشل في الحصول على استجابة بعد تجربة {len(keys_tried)} مفتاح")
        return None

    def get_stats_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص إحصائيات المفاتيح"""
        total_keys = len(self.keys)
        active_keys = len([k for k in self.keys if k.status == KeyStatus.ACTIVE])
        cooldown_keys = len([k for k in self.keys if k.cooldown_until and datetime.now() < k.cooldown_until])
        invalid_keys = len([k for k in self.keys if k.status == KeyStatus.INVALID])
        
        total_requests = sum(k.total_requests for k in self.keys)
        total_successes = sum(k.success_count for k in self.keys)
        total_errors = sum(k.error_count for k in self.keys)
        
        success_rate = (total_successes / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "total_keys": total_keys,
            "active_keys": active_keys,
            "cooldown_keys": cooldown_keys,
            "invalid_keys": invalid_keys,
            "available_keys": len(self.get_available_keys()),
            "total_requests": total_requests,
            "total_successes": total_successes,
            "total_errors": total_errors,
            "success_rate": round(success_rate, 2),
            "avg_response_time": round(sum(k.avg_response_time for k in self.keys if k.avg_response_time > 0) / max(1, len([k for k in self.keys if k.avg_response_time > 0])), 2)
        }

    def print_detailed_stats(self):
        """طباعة إحصائيات مفصلة"""
        stats = self.get_stats_summary()
        
        print("\n" + "="*60)
        print("📊 إحصائيات مفاتيح Gemini API")
        print("="*60)
        print(f"🔑 إجمالي المفاتيح: {stats['total_keys']}")
        print(f"✅ مفاتيح نشطة: {stats['active_keys']}")
        print(f"🕒 مفاتيح في فترة تبريد: {stats['cooldown_keys']}")
        print(f"❌ مفاتيح غير صالحة: {stats['invalid_keys']}")
        print(f"🟢 مفاتيح متاحة: {stats['available_keys']}")
        print(f"📈 معدل النجاح: {stats['success_rate']}%")
        print(f"⏱️ متوسط وقت الاستجابة: {stats['avg_response_time']} ثانية")
        print(f"📊 إجمالي الطلبات: {stats['total_requests']}")
        print("="*60)

    def reset_key_cooldowns(self):
        """إعادة تعيين جميع فترات التبريد (للطوارئ)"""
        with self.lock:
            for key_info in self.keys:
                key_info.cooldown_until = None
                if key_info.status in [KeyStatus.COOLDOWN, KeyStatus.RATE_LIMITED, KeyStatus.ERROR]:
                    key_info.status = KeyStatus.UNKNOWN

            self.save_stats()
            print("🔄 تم إعادة تعيين جميع فترات التبريد")

    def test_all_keys(self) -> Dict[str, Any]:
        """اختبار جميع المفاتيح وإرجاع تقرير مفصل"""
        print("🧪 بدء اختبار جميع المفاتيح...")

        results = {
            "total_tested": 0,
            "working_keys": 0,
            "failed_keys": 0,
            "key_results": []
        }

        test_prompt = "Hello, this is a test message. Please respond with 'Test successful'."

        for i, key_info in enumerate(self.keys):
            print(f"🔍 اختبار المفتاح {i + 1}/{len(self.keys)}...")

            key_result = {
                "index": i + 1,
                "key_preview": key_info.key[:12] + "..." + key_info.key[-8:],
                "status": "unknown",
                "error": None,
                "response_time": 0
            }

            try:
                # تكوين مؤقت للاختبار
                genai.configure(api_key=key_info.key)
                test_model = genai.GenerativeModel(self.model_name)

                start_time = time.time()
                response = test_model.generate_content(test_prompt)
                response_time = time.time() - start_time

                if response and response.text:
                    key_result["status"] = "working"
                    key_result["response_time"] = round(response_time, 2)
                    results["working_keys"] += 1

                    # تحديث معلومات المفتاح
                    self.mark_key_success(i, response_time)
                    print(f"✅ المفتاح {i + 1}: يعمل ({response_time:.2f}s)")
                else:
                    raise Exception("استجابة فارغة")

            except Exception as e:
                error_msg = str(e).lower()
                key_result["status"] = "failed"
                key_result["error"] = str(e)
                results["failed_keys"] += 1

                # تحديد نوع الخطأ
                if "api_key" in error_msg or "invalid" in error_msg:
                    self.mark_key_error(i, str(e), KeyStatus.INVALID)
                    print(f"❌ المفتاح {i + 1}: غير صالح")
                elif "quota" in error_msg or "limit" in error_msg:
                    self.mark_key_error(i, str(e), KeyStatus.QUOTA_EXCEEDED)
                    print(f"⚠️ المفتاح {i + 1}: وصل للحد الأقصى")
                else:
                    self.mark_key_error(i, str(e), KeyStatus.ERROR)
                    print(f"❌ المفتاح {i + 1}: خطأ - {e}")

            results["key_results"].append(key_result)
            results["total_tested"] += 1

            # انتظار قصير بين الاختبارات
            time.sleep(0.5)

        # إعادة تكوين المفتاح الأفضل
        best_key = self.get_best_key()
        if best_key:
            self.configure_client(best_key[0])

        print(f"📊 انتهى الاختبار: {results['working_keys']} يعمل، {results['failed_keys']} فاشل")
        return results

    def optimize_key_priorities(self):
        """تحسين أولويات المفاتيح بناءً على الأداء"""
        with self.lock:
            # ترتيب المفاتيح حسب الأداء
            sorted_keys = sorted(self.keys, key=lambda k: (
                k.error_count / max(1, k.total_requests),  # معدل الخطأ
                k.avg_response_time,  # وقت الاستجابة
                -k.success_count  # عدد النجاحات (عكسي)
            ))

            # تحديث الأولويات
            for i, key_info in enumerate(sorted_keys):
                if i < 10:
                    key_info.priority = 1  # أولوية عالية
                elif i < 30:
                    key_info.priority = 2  # أولوية متوسطة
                else:
                    key_info.priority = 3  # أولوية منخفضة

            self.save_stats()
            print("🎯 تم تحسين أولويات المفاتيح")

    def get_key_health_report(self) -> str:
        """إنشاء تقرير صحة المفاتيح"""
        current_time = datetime.now()

        report = "\n" + "="*80 + "\n"
        report += "🏥 تقرير صحة مفاتيح Gemini API\n"
        report += "="*80 + "\n"

        # إحصائيات عامة
        stats = self.get_stats_summary()
        report += f"📊 الإحصائيات العامة:\n"
        report += f"   • إجمالي المفاتيح: {stats['total_keys']}\n"
        report += f"   • مفاتيح متاحة: {stats['available_keys']}\n"
        report += f"   • معدل النجاح: {stats['success_rate']}%\n"
        report += f"   • متوسط وقت الاستجابة: {stats['avg_response_time']}s\n\n"

        # تفاصيل المفاتيح
        report += "🔑 تفاصيل المفاتيح:\n"
        report += "-" * 80 + "\n"

        for i, key_info in enumerate(self.keys[:20]):  # أول 20 مفتاح فقط
            status_emoji = {
                KeyStatus.ACTIVE: "🟢",
                KeyStatus.QUOTA_EXCEEDED: "🔴",
                KeyStatus.INVALID: "❌",
                KeyStatus.RATE_LIMITED: "🟡",
                KeyStatus.COOLDOWN: "🕒",
                KeyStatus.ERROR: "⚠️",
                KeyStatus.UNKNOWN: "⚪"
            }.get(key_info.status, "⚪")

            key_preview = key_info.key[:8] + "..." + key_info.key[-6:]

            report += f"{status_emoji} مفتاح {i+1:2d}: {key_preview} "
            report += f"(أولوية: {key_info.priority}, "
            report += f"نجح: {key_info.success_count}, "
            report += f"فشل: {key_info.error_count})\n"

            if key_info.cooldown_until and current_time < key_info.cooldown_until:
                remaining = key_info.cooldown_until - current_time
                report += f"           🕒 فترة تبريد: {remaining.total_seconds()/60:.1f} دقيقة\n"

            if key_info.last_error:
                report += f"           ⚠️ آخر خطأ: {key_info.last_error[:50]}...\n"

        if len(self.keys) > 20:
            report += f"... و {len(self.keys) - 20} مفتاح آخر\n"

        report += "="*80 + "\n"

        return report

    def export_stats_to_json(self, filename: str = None) -> str:
        """تصدير الإحصائيات إلى ملف JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gemini_stats_export_{timestamp}.json"

        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "summary": self.get_stats_summary(),
                "keys": []
            }

            for i, key_info in enumerate(self.keys):
                key_data = {
                    "index": i + 1,
                    "key_preview": key_info.key[:12] + "..." + key_info.key[-8:],
                    "status": key_info.status.value,
                    "priority": key_info.priority,
                    "success_count": key_info.success_count,
                    "error_count": key_info.error_count,
                    "total_requests": key_info.total_requests,
                    "avg_response_time": key_info.avg_response_time,
                    "last_used": key_info.last_used.isoformat() if key_info.last_used else None,
                    "cooldown_until": key_info.cooldown_until.isoformat() if key_info.cooldown_until else None,
                    "last_error": key_info.last_error
                }
                export_data["keys"].append(key_data)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            print(f"📁 تم تصدير الإحصائيات إلى: {filename}")
            return filename

        except Exception as e:
            print(f"❌ خطأ في تصدير الإحصائيات: {e}")
            return ""

# مثيل عام للاستخدام
gemini_manager = None

def initialize_gemini_manager() -> SmartGeminiKeyManager:
    """تهيئة مدير مفاتيح Gemini العام"""
    global gemini_manager
    if gemini_manager is None:
        gemini_manager = SmartGeminiKeyManager()
    return gemini_manager

def get_gemini_manager() -> Optional[SmartGeminiKeyManager]:
    """الحصول على مدير مفاتيح Gemini"""
    return gemini_manager

# دوال مساعدة للتوافق مع الكود الحالي
def smart_gemini_request(prompt: str, max_retries: int = 3) -> Optional[str]:
    """دالة مساعدة للطلبات الذكية"""
    manager = get_gemini_manager()
    if manager:
        return manager.smart_request(prompt, max_retries)
    return None

def get_gemini_stats() -> Dict[str, Any]:
    """دالة مساعدة للحصول على الإحصائيات"""
    manager = get_gemini_manager()
    if manager:
        return manager.get_stats_summary()
    return {}

def test_gemini_keys() -> Dict[str, Any]:
    """دالة مساعدة لاختبار جميع المفاتيح"""
    manager = get_gemini_manager()
    if manager:
        return manager.test_all_keys()
    return {}

def reset_gemini_cooldowns():
    """دالة مساعدة لإعادة تعيين فترات التبريد"""
    manager = get_gemini_manager()
    if manager:
        manager.reset_key_cooldowns()

def get_gemini_health_report() -> str:
    """دالة مساعدة للحصول على تقرير الصحة"""
    manager = get_gemini_manager()
    if manager:
        return manager.get_key_health_report()
    return "❌ مدير مفاتيح Gemini غير مُهيأ"

if __name__ == "__main__":
    # اختبار النظام
    print("🚀 اختبار نظام إدارة مفاتيح Gemini الذكي")

    manager = SmartGeminiKeyManager()

    # طباعة الإحصائيات الأولية
    manager.print_detailed_stats()

    # اختبار طلب بسيط
    response = manager.smart_request("مرحبا، كيف حالك؟")
    if response:
        print(f"✅ استجابة الاختبار: {response[:100]}...")

    # طباعة تقرير الصحة
    print(manager.get_key_health_report())

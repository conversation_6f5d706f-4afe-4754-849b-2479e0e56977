# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الأوصاف القصيرة
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_description_generator():
    """اختبار مولد الأوصاف المحسن"""
    try:
        from enhanced_description_generator import EnhancedDescriptionGenerator
        
        print("🧪 اختبار مولد الأوصاف المحسن...")
        
        # إنشاء مولد الأوصاف
        desc_generator = EnhancedDescriptionGenerator()
        
        # بيانات مود تجريبية
        test_mod_data = {
            'name': 'Reckless and Burning Thorns Enchantments',
            'category': 'Addons',
            'creator_name': 'Test Creator'
        }
        
        # محتوى تجريبي
        test_content = """
        This mod adds two new enchantments: "Reckless" and "Burning Thorns", 
        plus a modification to the "Cowardice" enchantment to grant additional speed.
        These enchantments can be used on items to modify their combat properties.
        No specific commands are required to use the mod.
        """
        
        # تحليل المحتوى
        content_summary = desc_generator.analyze_mod_content(test_mod_data, test_content)
        print(f"📊 ملخص المحتوى: {content_summary}")
        
        # إنشاء الوصف الإنجليزي
        print("\n🔤 إنشاء الوصف الإنجليزي...")
        english_desc = desc_generator.generate_english_description(test_mod_data, content_summary)
        
        if english_desc:
            print(f"✅ تم إنشاء وصف إنجليزي ({len(english_desc)} حرف):")
            print("=" * 50)
            print(english_desc)
            print("=" * 50)
        else:
            print("❌ فشل في إنشاء الوصف الإنجليزي")
            return False
        
        # إنشاء الوصف العربي
        print("\n🔤 إنشاء الوصف العربي...")
        arabic_desc = desc_generator.generate_arabic_description(test_mod_data, content_summary, english_desc)
        
        if arabic_desc:
            print(f"✅ تم إنشاء وصف عربي ({len(arabic_desc)} حرف):")
            print("=" * 50)
            print(arabic_desc)
            print("=" * 50)
        else:
            print("❌ فشل في إنشاء الوصف العربي")
            return False
        
        # فحص طول الأوصاف
        if len(english_desc) >= 200 and len(arabic_desc) >= 200:
            print("✅ الأوصاف طويلة بما فيه الكفاية!")
            return True
        else:
            print(f"⚠️ الأوصاف قصيرة: إنجليزي={len(english_desc)}, عربي={len(arabic_desc)}")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_fallback_system():
    """اختبار النظام الاحتياطي"""
    print("\n🧪 اختبار النظام الاحتياطي...")
    
    # محاكاة عدم توفر النظام المحسن
    try:
        # هذا سيختبر النظام التقليدي المحسن
        mod_name = "Test Enchantments Mod"
        mod_category = "Addons"
        clean_context = """
        Key Features:
        - Adds "Reckless" enchantment for increased damage
        - Adds "Burning Thorns" enchantment for fire damage reflection
        - Modifies "Cowardice" enchantment to grant speed boost
        - Compatible with all item types
        - No commands required for usage
        """
        
        # النظام التقليدي المحسن
        prompt = f"""
        You are a Minecraft mod developer writing a comprehensive, engaging description for your mod.
        Create a detailed description that will attract players and clearly explain what your mod offers.

        **Mod Name:** {mod_name}
        **Category:** {mod_category}

        **Mod Features and Content:**
        {clean_context}

        **Task:**
        Write a detailed, engaging description (minimum 200 words) that thoroughly explains what this mod adds and how to use it.
        Include specific features, gameplay mechanics, and practical usage information.

        **REQUIREMENTS:**
        - Write at least 200-300 words for a comprehensive description
        - Start with what the mod allows/enables players to do
        - Include specific commands if mentioned (like /function commands)
        - Mention crafting recipes or materials if specified
        - Describe new blocks, items, mobs, or mechanics in detail
        - Include gameplay benefits and improvements
        - Use engaging but professional language
        - Add emojis to make it more attractive (🎮⚔️🏗️🔥✨)
        - Structure with clear paragraphs
        - End with encouragement to try the mod

        **Output format:**
        [DESCRIPTION]
        Your comprehensive, engaging description here.
        [/DESCRIPTION]
        """
        
        print("📝 مثال على الـ prompt المحسن:")
        print("=" * 50)
        print(prompt[:500] + "...")
        print("=" * 50)
        
        print("✅ النظام الاحتياطي جاهز للاستخدام")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام الاحتياطي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إصلاح مشكلة الأوصاف القصيرة")
    print("=" * 60)
    
    # اختبار النظام المحسن
    enhanced_test = test_enhanced_description_generator()
    
    # اختبار النظام الاحتياطي
    fallback_test = test_fallback_system()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   النظام المحسن: {'✅ نجح' if enhanced_test else '❌ فشل'}")
    print(f"   النظام الاحتياطي: {'✅ نجح' if fallback_test else '❌ فشل'}")
    
    if enhanced_test or fallback_test:
        print("\n✅ تم إصلاح مشكلة الأوصاف القصيرة!")
        print("💡 الآن ستحصل على أوصاف أطول وأكثر تفصيلاً")
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إلى حل")
    
    return enhanced_test or fallback_test

if __name__ == "__main__":
    main()

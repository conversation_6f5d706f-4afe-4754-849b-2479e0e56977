{"zombiegirls|bythebot03": {"mod_name": "ZombieGirls", "creator_name": "ByTheBot03", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/https%3A%2F%2Fstorage.googleapis.com%2Fdownload-e33a2.firebasestorage.app%2Fmods%2FZombieGirls_1751890456_tivf7aft.mcpack?alt=media", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1751890467.6008556, "published_date": "2025-07-07 12:14:27"}, "revive me revive loot saver chest|bycarchi77": {"mod_name": "Revive Me! - Revive & Loot Saver Chest", "creator_name": "ByCarchi77", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/https%3A%2F%2Fstorage.googleapis.com%2Fdownload-e33a2.firebasestorage.app%2Fmods%2FRevive_Me_115_-_1219x_1752006870_zwyxudrl.mcaddon?alt=media", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1752006974.6166992, "published_date": "2025-07-08 20:36:14"}, "phase blocks walk through any block|byfour worlds studios": {"mod_name": "Phase Blocks (Walk through any Block!)", "creator_name": "ByFour Worlds Studios", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/combined_modbin_1754065257_14s7pv0x.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754065660.1220155, "published_date": "2025-08-01 16:27:40"}, "mc but you get any block you look at|bybony162": {"mod_name": "MC But You Get Any Block You Look At", "creator_name": "ByBONY162", "source_url": "", "download_url": "https://media.forgecdn.net/attachments/description/null/description_c68ac84d-44cd-4469-845c-7363451fea2d.png", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754078817.1977885, "published_date": "2025-08-01 20:06:57"}, "bare bones stuff|by pochilito": {"mod_name": "Bare Bones & Stuff", "creator_name": "By Pochilit<PERSON>", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FBarebonesNStuff_15_V2_1754167401_zbogefk4.mcpack?alt=mediahttps://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FBarebonesNStuff_15_V2_1754167401_zbogefk4.mcpack?alt=media", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754167632.1239896, "published_date": "2025-08-02 20:47:12"}, "bare bones stuff|bypochilito": {"mod_name": "Bare Bones & Stuff", "creator_name": "ByPochilito", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/BarebonesNStuff_151_1754305015_get6j1pw.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754305126.0145013, "published_date": "2025-08-04 10:58:46"}, "glowshot projectile trails|just flaash": {"mod_name": "Glow-Shot Projectile Trails", "creator_name": "<PERSON>", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FGlow-Shot_1754214663_6zpf42g4.mcpack?alt=media", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754214780.3743997, "published_date": "2025-08-03 09:53:00"}, "armored ghast addon happy ghast equipment|reyro project": {"mod_name": "Armored Ghast [Addon] [Happy Ghast] [Equipment]", "creator_name": "Reyro Project", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FArmGhast_1754216274_1s1vxvxj.mcaddon?alt=media", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754216398.170992, "published_date": "2025-08-03 10:19:58"}, "aplok guns|gabrielaplok": {"mod_name": "Aplok Guns", "creator_name": "GabrielAplok", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Aplok-Guns-v117_1754302259_52owyt2x.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754302350.737525, "published_date": "2025-08-04 10:12:30"}, "horror environment achievement friendly update|byzorrocraft1": {"mod_name": "Horror Environment - Achievement friendly Update", "creator_name": "ByZorrocraft1", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Aplok-Guns-v117_1754232953_mxhi1phs.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754233077.6978452, "published_date": "2025-08-03 14:57:57"}, "pillage generator|th3emilis": {"mod_name": "Pillage Generator", "creator_name": "Th3<PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Pillage_Generator_210_1754236112_46584qok.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754236128.097918, "published_date": "2025-08-03 15:48:48"}, "conqueror of villagers|dodofilms": {"mod_name": "Conqueror Of Villagers", "creator_name": "DodoFilms", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Conqueror_Of_Villagersbin_1754237037_vr42o6js.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754237111.9194937, "published_date": "2025-08-03 16:05:11"}, "xp crystals find xp on your adventures|four worlds studios": {"mod_name": "XP Crystals (Find XP on your Adventures)", "creator_name": "Four Worlds Studios", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/XP_Crystals_Find_XP_on_your_Adventures_bin_1754239285_rb1qvcgs.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754239415.727421, "published_date": "2025-08-03 16:43:35"}, "vibrant visuals extreme shader|risabgamerz": {"mod_name": "Vibrant Visuals Extreme Shader", "creator_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Vibrant_Visuals_Extreme_v14_1754241374_xul1t6aj.mcpack", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754241436.5240726, "published_date": "2025-08-03 17:17:16"}, "esp texture pack bed player chests mobs and more|mod mcpe": {"mod_name": "ESP Texture Pack | Bed, Player, Chests, mobs and more!", "creator_name": "Mod MCPE", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/ESP_Texture_Pack_1754250134_bd4ur43j.mcpack", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754250286.648589, "published_date": "2025-08-03 19:44:46"}, "plants vs zombies|bony162": {"mod_name": "Plants vs Zombies", "creator_name": "BONY162", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Plants_vs_Zombies_BedrockEdition_v11_BONY162_1__1754300263_banxvltu.mcaddon", "mod_size": null, "version": null, "category": "Maps", "description": null, "published_at": 1754300383.3013258, "published_date": "2025-08-04 09:39:43"}, "avatar addon overhaul|glitchyturtle": {"mod_name": "Avatar Addon OVERHAUL", "creator_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Avatar_Addon_125_Beta__1754300966_sjm5p5op.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754301023.5144498, "published_date": "2025-08-04 09:50:23"}, "chunk biomes survival|spilledwater": {"mod_name": "Chunk Biomes Survival", "creator_name": "Spilledwater", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/ChunkBiomesSurvival_1754301296_nfanoh4q.mcaddon", "mod_size": null, "version": null, "category": "Maps", "description": null, "published_at": 1754301318.479886, "published_date": "2025-08-04 09:55:18"}, "rg shader v renderdragon shader|risabgamerz": {"mod_name": "RG Shader v3 Renderdragon Shader", "creator_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/RG_SHADER_v308_1754302079_4y4u289i.mcpack", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754302144.3001592, "published_date": "2025-08-04 10:09:04"}, "glowshot projectile trails|byjust flaash": {"mod_name": "Glow-Shot Projectile Trails", "creator_name": "ByJust Flaash", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/mods_Glow-Shot_1754214663_6zpf42g4_2__1754303789_pxb2jt6m.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754303835.9557598, "published_date": "2025-08-04 10:37:15"}, "piggyback addon|daniiye": {"mod_name": "Piggy<PERSON>", "creator_name": "<PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Piggyback_by_<PERSON><PERSON>_-_v105_1754304235_wvghmi22.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754304365.414416, "published_date": "2025-08-04 10:46:05"}, "solid mobs|bydaniiye": {"mod_name": "Solid Mobs", "creator_name": "ByDanii<PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Solid_Mobs_by_<PERSON><PERSON>_-_v103_1754304473_75q9my9e.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754304576.6441565, "published_date": "2025-08-04 10:49:36"}, "fishing enhanced|byharrybeest": {"mod_name": "Fishing Enhanced", "creator_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Fishing_Enhanced_Desert_U4__1754305707_bngv65kf.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754305775.9444659, "published_date": "2025-08-04 11:09:35"}, "better boats|bycarchi77": {"mod_name": "Better Boats", "creator_name": "ByCarchi77", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Better_Boatsbin_1754306113_t5p8lcbl.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754306178.2752297, "published_date": "2025-08-04 11:16:18"}, "js rpg party|byj.s.art": {"mod_name": "JS' RPG Party", "creator_name": "ByJ.S.Art", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/JS_RPG_Party_100__1754306541_d8fuldc2.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754306567.0844667, "published_date": "2025-08-04 11:22:47"}, "coldbar indicator|qduoubp": {"mod_name": "Coldbar Indicator", "creator_name": "qduoubp", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Coldbar_Indicatorbin_1754479337_hb343494.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754479412.6769595, "published_date": "2025-08-06 11:23:32"}, "roll and stamina parkour expansion|qduoubp": {"mod_name": "Roll and Stamina: Parkour Expansion", "creator_name": "qduoubp", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Roll_and_Stamina_Parkour_Expansionbin_1754479582_f60f2wab.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754479638.770781, "published_date": "2025-08-06 11:27:18"}, "magic broomstick witch or wizard|four worlds studios": {"mod_name": "Magic Broomstick (Witch or Wizard?)", "creator_name": "Four Worlds Studios", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Magic_Broomstick_Witch_or_Wizard_bin_1754486102_dmm3c2qo.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754486187.9621236, "published_date": "2025-08-06 13:16:27"}, "sooty chimneys bedrock edition|brunumgames": {"mod_name": "<PERSON><PERSON> (Bedrock Edition)", "creator_name": "B<PERSON><PERSON>Games", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Sooty_Chimneys_BE_12_1754486311_dd2xjxd6.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754486392.957248, "published_date": "2025-08-06 13:19:52"}, "raiyons more enchantments|lord raiyon": {"mod_name": "Raiyon's More Enchantments", "creator_name": "Lord <PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Raiyon_s_More_Enchantmentsbin_1754487510_xn3u7cdf.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754487561.359464, "published_date": "2025-08-06 13:39:21"}, "sit on player unofficial bedrock port of player ladder|byzepaii": {"mod_name": "Sit on Player – Unofficial Bedrock Port of Player Ladder", "creator_name": "ByZepaii", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Sit_On_Player_101__1754500508_gfgvi4oo.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754500551.2035263, "published_date": "2025-08-06 17:15:51"}, "ore visualizer|bruhh69": {"mod_name": "Ore Visualizer", "creator_name": "Bruhh69", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Ore_Visualizer_v103_1754500706_4d2sh271.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754500791.5405433, "published_date": "2025-08-06 17:19:51"}, "sao damage indicator|wartave": {"mod_name": "SAO Damage Indicator", "creator_name": "Wartave", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/SAO_Damage_Indicator_v102_1754501091_nb25bm9k.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754501115.618544, "published_date": "2025-08-06 17:25:15"}}
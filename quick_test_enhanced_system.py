# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام المحسن لإنشاء الأوصاف
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_description_system():
    """اختبار سريع للنظام المحسن"""
    print("🧪 اختبار سريع للنظام المحسن لإنشاء الأوصاف")
    print("="*60)
    
    try:
        # استيراد النظام المحسن
        from smart_gemini_key_manager import SmartGeminiKeyManager
        from enhanced_description_generator import EnhancedDescriptionGenerator
        
        print("✅ تم استيراد النظام المحسن بنجاح")
        
        # إنشاء مدير المفاتيح
        manager = SmartGeminiKeyManager()
        print(f"🔑 تم تحميل {len(manager.keys)} مفتاح")
        
        # إحصائيات أولية
        stats = manager.get_stats_summary()
        print(f"📊 مفاتيح متاحة: {stats['available_keys']}")
        
        # اختبار بسيط
        print("\n🔍 اختبار طلب بسيط...")
        response = manager.smart_request("قل مرحبا")
        
        if response:
            print(f"✅ استجابة ناجحة: {response[:50]}...")
        else:
            print("❌ لا توجد استجابة")
            return False
        
        # اختبار مولد الأوصاف
        print("\n📝 اختبار مولد الأوصاف...")
        generator = EnhancedDescriptionGenerator()
        
        test_mod_data = {
            'name': 'Test Mod',
            'category': 'Addons',
            'creator_name': 'Test Creator'
        }
        
        test_content = "This is a test mod with new blocks and items"
        
        english_desc, arabic_desc = generator.generate_descriptions(test_mod_data, test_content)
        
        print(f"📖 وصف إنجليزي: {len(english_desc)} حرف")
        print(f"📖 وصف عربي: {len(arabic_desc)} حرف")
        
        if len(english_desc) > 200 and len(arabic_desc) > 200:
            print("✅ النظام يعمل بشكل ممتاز!")
            return True
        else:
            print("⚠️ الأوصاف قصيرة، قد تحتاج لمراجعة")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود الملفات:")
        print("   • smart_gemini_key_manager.py")
        print("   • enhanced_description_generator.py")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_api_keys():
    """اختبار مفاتيح API"""
    print("\n🔑 اختبار مفاتيح API...")
    
    try:
        import json
        
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # عد المفاتيح
        api_keys = []
        
        # المفتاح الفردي
        single_key = config.get("GEMINI_API_KEY")
        if single_key and single_key.strip() and single_key != "YOUR_GEMINI_KEY_HERE":
            api_keys.append(single_key.strip())
        
        # قائمة المفاتيح
        keys_list = config.get("gemini_api_keys", [])
        for key in keys_list:
            if key and key.strip() and key != "YOUR_GEMINI_KEY_HERE" and key not in api_keys:
                api_keys.append(key.strip())
        
        print(f"📊 تم العثور على {len(api_keys)} مفتاح API")
        
        if len(api_keys) > 0:
            print("✅ مفاتيح API متوفرة")
            return True
        else:
            print("❌ لا توجد مفاتيح API صالحة")
            return False
            
    except FileNotFoundError:
        print("❌ ملف api_keys.json غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة مفاتيح API: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء الاختبار السريع للنظام المحسن")
    print("="*60)
    
    # اختبار مفاتيح API
    keys_ok = test_api_keys()
    
    if not keys_ok:
        print("\n❌ فشل اختبار مفاتيح API")
        print("💡 تأكد من:")
        print("   1. وجود ملف api_keys.json")
        print("   2. إضافة مفاتيح Gemini API صالحة")
        return
    
    # اختبار النظام المحسن
    system_ok = test_enhanced_description_system()
    
    print("\n" + "="*60)
    if system_ok:
        print("🎉 النظام المحسن يعمل بشكل ممتاز!")
        print("✅ جاهز لإنشاء أوصاف عالية الجودة")
        print("\n📋 الخطوات التالية:")
        print("   1. شغل الأداة الرئيسية: python mod_processor_broken_final.py")
        print("   2. جرب إنشاء وصف لمود")
        print("   3. ستلاحظ تحسن كبير في جودة الأوصاف")
    else:
        print("❌ النظام المحسن يحتاج لمراجعة")
        print("🔧 راجع الأخطاء أعلاه وأعد المحاولة")
    print("="*60)

if __name__ == "__main__":
    main()
